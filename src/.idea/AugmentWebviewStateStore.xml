<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;2079dcf3-a9bf-4398-bffc-5ea16b77bcab&quot;,&quot;conversations&quot;:{&quot;2079dcf3-a9bf-4398-bffc-5ea16b77bcab&quot;:{&quot;id&quot;:&quot;2079dcf3-a9bf-4398-bffc-5ea16b77bcab&quot;,&quot;createdAtIso&quot;:&quot;2025-07-03T00:16:31.821Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-03T18:58:55.017Z&quot;,&quot;chatHistory&quot;:[{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;b56ec87f-e014-44b7-bbe3-5da48db8ad6d&quot;,&quot;request_message&quot;:&quot;test&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;test&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;I see you're testing the system. I'm ready to help you with your code and software engineering questions. \n\nBased on the codebase context I can see, you have a recipe management application built with Next.js, TypeScript, and Supabase that includes:\n\n- Recipe management with CRUD operations\n- RSS feed integration for importing recipes\n- Recipe scraping functionality\n- Authentication with Supabase\n- Comprehensive test coverage\n\nWhat would you like assistance with?\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;src/lib/__tests__/utils.test.ts:0-638\nsrc/lib/__tests__/utils.test.ts:638-1383\nsrc/lib/__tests__/utils.test.ts:1383-1888\nsrc/app/api/rss-feeds/test/route.ts:0-109\nsrc/app/api/rss-feeds/test/route.ts:109-944\nsrc/app/api/rss-feeds/test/route.ts:944-1893\nsrc/app/api/rss-feeds/test/route.ts:1893-2130\nsrc/lib/__tests__/tagUtils.test.ts:0-796\nsrc/lib/__tests__/tagUtils.test.ts:796-1410\nsrc/lib/__tests__/tagUtils.test.ts:1410-2267\nsrc/lib/__tests__/tagUtils.test.ts:2267-2803\nsrc/lib/__tests__/tagUtils.test.ts:2803-3516\nsrc/lib/__tests__/tagUtils.test.ts:3516-3939\nsrc/components/__tests__/RecipeDetails.test.tsx:0-703\nsrc/components/__tests__/RecipeDetails.test.tsx:703-1541\nsrc/components/__tests__/RecipeDetails.test.tsx:1541-2132\nsrc/components/__tests__/RecipeDetails.test.tsx:2132-2661\nsrc/components/__tests__/RecipeDetails.test.tsx:2661-3436\nsrc/components/__tests__/RecipeDetails.test.tsx:3436-3874\nsrc/components/__tests__/RecipeDetails.test.tsx:3874-4391\nsrc/components/__tests__/RecipeCard.test.tsx:0-319\nsrc/components/__tests__/RecipeCard.test.tsx:319-1127\nsrc/components/__tests__/RecipeCard.test.tsx:1127-1951\nsrc/contexts/__tests__/AuthContext.test.tsx:0-800\nsrc/contexts/__tests__/AuthContext.test.tsx:800-1171\nsrc/contexts/__tests__/AuthContext.test.tsx:1171-1961\nsrc/lib/__tests__/recipeScraper.test.ts:0-84\nsrc/lib/__tests__/recipeScraper.test.ts:84-873\nsrc/lib/__tests__/recipeScraper.test.ts:873-1490\nsrc/lib/__tests__/recipeScraper.test.ts:1490-2195\nsrc/lib/__tests__/recipeScraper.test.ts:2195-2413\nsrc/lib/__tests__/recipeScraper.test.ts:2413-3120\nsrc/lib/rssImporter.ts:0-573\nsrc/lib/rssImporter.ts:14392-15199\nsrc/app/api/rss-feeds/route.ts:0-239\nsrc/app/api/rss-feeds/route.ts:1524-2475\nsrc/app/api/rss-feeds/route.ts:2475-3203\nsrc/components/AddRssFeedModal.tsx:0-574\nsrc/components/AddRssFeedModal.tsx:1211-1863\nsrc/app/api/rss-feeds/cron/route.ts:0-170\nsrc/app/api/rss-feeds/cron/route.ts:170-1005\nsrc/app/api/rss-feeds/cron/route.ts:1005-1938\nsrc/hooks/useRssFeeds.ts:5234-5712\nsrc/lib/supabase.ts:7979-8181\nsrc/app/api/rss-feeds/setup/route.ts:0-781\nsrc/app/api/rss-feeds/import/route.ts:164-1076\nsrc/app/card/[id]/page.tsx:0-737&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;test&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/recipe-picker&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/recipe-picker&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-03T16:42:05.537Z&quot;,&quot;response_text&quot;:&quot;I see you're testing the system. I'm ready to help you with your code and software engineering questions. \n\nBased on the codebase context I can see, you have a recipe management application built with Next.js, TypeScript, and Supabase that includes:\n\n- Recipe management with CRUD operations\n- RSS feed integration for importing recipes\n- Recipe scraping functionality\n- Authentication with Supabase\n- Comprehensive test coverage\n\nWhat would you like assistance with?\n&quot;,&quot;workspace_file_chunks&quot;:[{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;638&quot;,&quot;blob_name&quot;:&quot;f397c44f6adf135767e0e1d0e2d0a94c0447aa0b9a8322bcb0820a3e584ea29a&quot;},{&quot;char_start&quot;:&quot;638&quot;,&quot;char_end&quot;:&quot;1383&quot;,&quot;blob_name&quot;:&quot;f397c44f6adf135767e0e1d0e2d0a94c0447aa0b9a8322bcb0820a3e584ea29a&quot;},{&quot;char_start&quot;:&quot;1383&quot;,&quot;char_end&quot;:&quot;1888&quot;,&quot;blob_name&quot;:&quot;f397c44f6adf135767e0e1d0e2d0a94c0447aa0b9a8322bcb0820a3e584ea29a&quot;},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;109&quot;,&quot;blob_name&quot;:&quot;22dcad1d943f57632d3010be146a36867859723aa0cbaa3a420e8104b1a28bce&quot;},{&quot;char_start&quot;:&quot;109&quot;,&quot;char_end&quot;:&quot;944&quot;,&quot;blob_name&quot;:&quot;22dcad1d943f57632d3010be146a36867859723aa0cbaa3a420e8104b1a28bce&quot;},{&quot;char_start&quot;:&quot;944&quot;,&quot;char_end&quot;:&quot;1893&quot;,&quot;blob_name&quot;:&quot;22dcad1d943f57632d3010be146a36867859723aa0cbaa3a420e8104b1a28bce&quot;},{&quot;char_start&quot;:&quot;1893&quot;,&quot;char_end&quot;:&quot;2130&quot;,&quot;blob_name&quot;:&quot;22dcad1d943f57632d3010be146a36867859723aa0cbaa3a420e8104b1a28bce&quot;},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;796&quot;,&quot;blob_name&quot;:&quot;3e411b7408231a9239a9bf2f2f719f1d63fbf16c41ac280186217dd2769576d1&quot;},{&quot;char_start&quot;:&quot;796&quot;,&quot;char_end&quot;:&quot;1410&quot;,&quot;blob_name&quot;:&quot;3e411b7408231a9239a9bf2f2f719f1d63fbf16c41ac280186217dd2769576d1&quot;},{&quot;char_start&quot;:&quot;1410&quot;,&quot;char_end&quot;:&quot;2267&quot;,&quot;blob_name&quot;:&quot;3e411b7408231a9239a9bf2f2f719f1d63fbf16c41ac280186217dd2769576d1&quot;},{&quot;char_start&quot;:&quot;2267&quot;,&quot;char_end&quot;:&quot;2803&quot;,&quot;blob_name&quot;:&quot;3e411b7408231a9239a9bf2f2f719f1d63fbf16c41ac280186217dd2769576d1&quot;},{&quot;char_start&quot;:&quot;2803&quot;,&quot;char_end&quot;:&quot;3516&quot;,&quot;blob_name&quot;:&quot;3e411b7408231a9239a9bf2f2f719f1d63fbf16c41ac280186217dd2769576d1&quot;},{&quot;char_start&quot;:&quot;3516&quot;,&quot;char_end&quot;:&quot;3939&quot;,&quot;blob_name&quot;:&quot;3e411b7408231a9239a9bf2f2f719f1d63fbf16c41ac280186217dd2769576d1&quot;},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;703&quot;,&quot;blob_name&quot;:&quot;f3a2c74bb0666a0feecb145c8244d331bd6880e6d0493ca85fc2685371b99852&quot;},{&quot;char_start&quot;:&quot;703&quot;,&quot;char_end&quot;:&quot;1541&quot;,&quot;blob_name&quot;:&quot;f3a2c74bb0666a0feecb145c8244d331bd6880e6d0493ca85fc2685371b99852&quot;},{&quot;char_start&quot;:&quot;1541&quot;,&quot;char_end&quot;:&quot;2132&quot;,&quot;blob_name&quot;:&quot;f3a2c74bb0666a0feecb145c8244d331bd6880e6d0493ca85fc2685371b99852&quot;},{&quot;char_start&quot;:&quot;2132&quot;,&quot;char_end&quot;:&quot;2661&quot;,&quot;blob_name&quot;:&quot;f3a2c74bb0666a0feecb145c8244d331bd6880e6d0493ca85fc2685371b99852&quot;},{&quot;char_start&quot;:&quot;2661&quot;,&quot;char_end&quot;:&quot;3436&quot;,&quot;blob_name&quot;:&quot;f3a2c74bb0666a0feecb145c8244d331bd6880e6d0493ca85fc2685371b99852&quot;},{&quot;char_start&quot;:&quot;3436&quot;,&quot;char_end&quot;:&quot;3874&quot;,&quot;blob_name&quot;:&quot;f3a2c74bb0666a0feecb145c8244d331bd6880e6d0493ca85fc2685371b99852&quot;},{&quot;char_start&quot;:&quot;3874&quot;,&quot;char_end&quot;:&quot;4391&quot;,&quot;blob_name&quot;:&quot;f3a2c74bb0666a0feecb145c8244d331bd6880e6d0493ca85fc2685371b99852&quot;},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;319&quot;,&quot;blob_name&quot;:&quot;c7ff84a4758358b605eaf417030af48607c475971a0ce60af56783e38f6e1064&quot;},{&quot;char_start&quot;:&quot;319&quot;,&quot;char_end&quot;:&quot;1127&quot;,&quot;blob_name&quot;:&quot;c7ff84a4758358b605eaf417030af48607c475971a0ce60af56783e38f6e1064&quot;},{&quot;char_start&quot;:&quot;1127&quot;,&quot;char_end&quot;:&quot;1951&quot;,&quot;blob_name&quot;:&quot;c7ff84a4758358b605eaf417030af48607c475971a0ce60af56783e38f6e1064&quot;},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;800&quot;,&quot;blob_name&quot;:&quot;39f12a56ed403b5bc261bcc12f4c4cc69a6c164737d5147dcb68402fd504f251&quot;},{&quot;char_start&quot;:&quot;800&quot;,&quot;char_end&quot;:&quot;1171&quot;,&quot;blob_name&quot;:&quot;39f12a56ed403b5bc261bcc12f4c4cc69a6c164737d5147dcb68402fd504f251&quot;},{&quot;char_start&quot;:&quot;1171&quot;,&quot;char_end&quot;:&quot;1961&quot;,&quot;blob_name&quot;:&quot;39f12a56ed403b5bc261bcc12f4c4cc69a6c164737d5147dcb68402fd504f251&quot;},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;84&quot;,&quot;blob_name&quot;:&quot;75631fbfde9224600f8d621124c39e99f1773b6e94eea0ad20975bcca84fa7b8&quot;},{&quot;char_start&quot;:&quot;84&quot;,&quot;char_end&quot;:&quot;873&quot;,&quot;blob_name&quot;:&quot;75631fbfde9224600f8d621124c39e99f1773b6e94eea0ad20975bcca84fa7b8&quot;},{&quot;char_start&quot;:&quot;873&quot;,&quot;char_end&quot;:&quot;1490&quot;,&quot;blob_name&quot;:&quot;75631fbfde9224600f8d621124c39e99f1773b6e94eea0ad20975bcca84fa7b8&quot;},{&quot;char_start&quot;:&quot;1490&quot;,&quot;char_end&quot;:&quot;2195&quot;,&quot;blob_name&quot;:&quot;75631fbfde9224600f8d621124c39e99f1773b6e94eea0ad20975bcca84fa7b8&quot;},{&quot;char_start&quot;:&quot;2195&quot;,&quot;char_end&quot;:&quot;2413&quot;,&quot;blob_name&quot;:&quot;75631fbfde9224600f8d621124c39e99f1773b6e94eea0ad20975bcca84fa7b8&quot;},{&quot;char_start&quot;:&quot;2413&quot;,&quot;char_end&quot;:&quot;3120&quot;,&quot;blob_name&quot;:&quot;75631fbfde9224600f8d621124c39e99f1773b6e94eea0ad20975bcca84fa7b8&quot;},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;573&quot;,&quot;blob_name&quot;:&quot;539422a495c791c2c8aca2224d184f8b60dbd92ca5d1385514c506e36daaf4e0&quot;},{&quot;char_start&quot;:&quot;14392&quot;,&quot;char_end&quot;:&quot;15199&quot;,&quot;blob_name&quot;:&quot;539422a495c791c2c8aca2224d184f8b60dbd92ca5d1385514c506e36daaf4e0&quot;},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;239&quot;,&quot;blob_name&quot;:&quot;961573e53493e49f4cf414df4a67eb8f69d958cb207ddfb79ae1e34ffa6fe4c3&quot;},{&quot;char_start&quot;:&quot;1524&quot;,&quot;char_end&quot;:&quot;2475&quot;,&quot;blob_name&quot;:&quot;961573e53493e49f4cf414df4a67eb8f69d958cb207ddfb79ae1e34ffa6fe4c3&quot;},{&quot;char_start&quot;:&quot;2475&quot;,&quot;char_end&quot;:&quot;3203&quot;,&quot;blob_name&quot;:&quot;961573e53493e49f4cf414df4a67eb8f69d958cb207ddfb79ae1e34ffa6fe4c3&quot;},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;574&quot;,&quot;blob_name&quot;:&quot;47eeb3c8a58619cac298f035d553977984236f07e99d47d5837862b88940dd14&quot;},{&quot;char_start&quot;:&quot;1211&quot;,&quot;char_end&quot;:&quot;1863&quot;,&quot;blob_name&quot;:&quot;47eeb3c8a58619cac298f035d553977984236f07e99d47d5837862b88940dd14&quot;},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;170&quot;,&quot;blob_name&quot;:&quot;8cb1c09f40c4c654388669d2bec14a0e65be227fe1310eea98330c53573e925c&quot;},{&quot;char_start&quot;:&quot;170&quot;,&quot;char_end&quot;:&quot;1005&quot;,&quot;blob_name&quot;:&quot;8cb1c09f40c4c654388669d2bec14a0e65be227fe1310eea98330c53573e925c&quot;},{&quot;char_start&quot;:&quot;1005&quot;,&quot;char_end&quot;:&quot;1938&quot;,&quot;blob_name&quot;:&quot;8cb1c09f40c4c654388669d2bec14a0e65be227fe1310eea98330c53573e925c&quot;},{&quot;char_start&quot;:&quot;5234&quot;,&quot;char_end&quot;:&quot;5712&quot;,&quot;blob_name&quot;:&quot;909ebc3a06b53cec3d7cec66ffc08274f2b377ae5a29dd2bbeb6af93aa2ad2ae&quot;},{&quot;char_start&quot;:&quot;7979&quot;,&quot;char_end&quot;:&quot;8181&quot;,&quot;blob_name&quot;:&quot;69f3497fbc03fe28fae1c8fe0e892bc351f8c162574f37de29e379c92b629946&quot;},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;781&quot;,&quot;blob_name&quot;:&quot;6e6b2188571d49ce82f7bdbf3b955a51fb6f82a10c091db3f6569074be9c72f2&quot;},{&quot;char_start&quot;:&quot;164&quot;,&quot;char_end&quot;:&quot;1076&quot;,&quot;blob_name&quot;:&quot;3ded15b27fc4838d63be3d02fbe6e8bd979bcd3904bb6c034eb0389b7a88aa72&quot;},{&quot;char_start&quot;:&quot;0&quot;,&quot;char_end&quot;:&quot;737&quot;,&quot;blob_name&quot;:&quot;33770dfbfec06d30695d630408409a21754842411a82ee992fe6c34bdc0ac806&quot;}]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;9bdedf7b-b19b-4df8-9182-ac9825e82b15&quot;,&quot;request_message&quot;:&quot;call the github tool to get root &quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;call the github tool to get root &quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01RmAtnYomuznN2nHC6NnmVJ&quot;,&quot;tool_name&quot;:&quot;github-api&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;/repos/justinxu421/recipe-picker\&quot;, \&quot;summary\&quot;: \&quot;Get repository information for the recipe-picker repository\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;I'll call the GitHub API tool to get information about the repository root.\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;call the github tool to get root &quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/recipe-picker&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/recipe-picker&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-03T17:58:37.302Z&quot;,&quot;response_text&quot;:&quot;I'll call the GitHub API tool to get information about the repository root.\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;eca8e3b6-13f6-4fef-a729-8d53cfc63d85&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;Here's the repository information for your recipe-picker project:\n\n- **Repository**: justinxu421/recipe-picker\n- **Created**: January 8, 2025\n- **Last Updated**: June 30, 2025\n- **Owner**: justinxu421\n- **Repository URL**: https://api.github.com/repos/justinxu421/recipe-picker\n\nThe repository is currently active and was last updated about 3 days ago. Is there anything specific about the repository you'd like to explore or work on?\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;created_at: '2025-01-08T02:33:42Z'\nname: recipe-picker\nowner:\n  login: justinxu421\n  url: https://api.github.com/users/justinxu421\nupdated_at: '2025-06-30T04:17:29Z'\nurl: https://api.github.com/repos/justinxu421/recipe-picker\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01RmAtnYomuznN2nHC6NnmVJ&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/recipe-picker&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/recipe-picker&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-03T17:58:45.345Z&quot;,&quot;response_text&quot;:&quot;Here's the repository information for your recipe-picker project:\n\n- **Repository**: justinxu421/recipe-picker\n- **Created**: January 8, 2025\n- **Last Updated**: June 30, 2025\n- **Owner**: justinxu421\n- **Repository URL**: https://api.github.com/repos/justinxu421/recipe-picker\n\nThe repository is currently active and was last updated about 3 days ago. Is there anything specific about the repository you'd like to explore or work on?\n&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-10aeebc0-9191-453e-97ee-c42908f6a4b8&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-9839cac6-37de-4d09-bf8e-2232fc0c8565&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-0f070edd-f872-4f98-9a9a-7d8ccb63f3cc&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{&quot;9bdedf7b-b19b-4df8-9182-ac9825e82b15;toolu_vrtx_01RmAtnYomuznN2nHC6NnmVJ&quot;:{&quot;requestId&quot;:&quot;9bdedf7b-b19b-4df8-9182-ac9825e82b15&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01RmAtnYomuznN2nHC6NnmVJ&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;created_at: '2025-01-08T02:33:42Z'\nname: recipe-picker\nowner:\n  login: justinxu421\n  url: https://api.github.com/users/justinxu421\nupdated_at: '2025-06-30T04:17:29Z'\nurl: https://api.github.com/repos/justinxu421/recipe-picker\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}}},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;isAgentConversation&quot;:true,&quot;baselineTimestamp&quot;:0,&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;d261d63a-ea80-41b0-aec8-f47603fac998&quot;},&quot;9894bb8b-8132-45a9-b123-017779316160&quot;:{&quot;id&quot;:&quot;9894bb8b-8132-45a9-b123-017779316160&quot;,&quot;createdAtIso&quot;:&quot;2025-07-03T18:58:54.982Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-03T18:58:54.982Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[],&quot;sortConversationsBy&quot;:&quot;lastMessageTimestamp&quot;,&quot;sendMode&quot;:&quot;send&quot;}" />
      </map>
    </option>
  </component>
</project>