declare module '@supabase/auth-ui-react' {
  import { ComponentType } from 'react';
  import { SupabaseClient } from '@supabase/supabase-js';

  interface AuthAppearanceVariables {
    default?: {
      colors?: Record<string, string>;
      space?: Record<string, string>;
      fontSizes?: Record<string, string>;
      fonts?: Record<string, string>;
      borderWidths?: Record<string, string>;
      radii?: Record<string, string>;
    };
  }

  interface AuthAppearance {
    theme?: unknown;
    variables?: AuthAppearanceVariables;
  }

  export interface AuthProps {
    supabaseClient: SupabaseClient;
    appearance?: AuthAppearance;
    providers?: string[];
    redirectTo?: string;
    onlyThirdPartyProviders?: boolean;
    magicLink?: boolean;
    showLinks?: boolean;
  }

  export const Auth: ComponentType<AuthProps>;
}
