export interface ScrapedRecipeData {
  title?: string;
  description?: string;
  ingredients?: string[];
  instructions?: string[];
  prepTime?: number;
  cookTime?: number;
  totalTime?: number;
  servings?: number;
  difficulty?: string;
  cuisine?: string;
  image?: string;
  author?: string;
  rating?: number;
  reviewCount?: number;
}

export interface RecipeScrapingResult {
  recipe?: ScrapedRecipeData;
  error?: string;
}

/**
 * Server-side recipe scraping function that directly fetches and parses HTML
 * This can be used in API routes and server-side contexts
 */
export async function scrapeRecipeFromUrlServerSide(
  url: string
): Promise<RecipeScrapingResult> {
  try {
    // Validate URL format
    try {
      new URL(url);
    } catch {
      return {
        error: 'Invalid URL format',
      };
    }

    // Fetch the webpage with comprehensive headers to avoid blocking
    const response = await fetch(url, {
      headers: {
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        Accept:
          'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        DNT: '1',
        Connection: 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Cache-Control': 'max-age=0',
      },
      // Add timeout to prevent hanging requests
      signal: AbortSignal.timeout(15000), // 15 second timeout
    });

    if (!response.ok) {
      const errorMessage =
        response.status === 403
          ? 'Access forbidden - website blocks scraping'
          : response.status === 404
            ? 'Page not found'
            : response.status === 429
              ? 'Rate limited - too many requests'
              : `Failed to fetch webpage (${response.status})`;

      return {
        error: errorMessage,
      };
    }

    const html = await response.text();

    // Extract recipe data from HTML
    const recipeData = extractRecipeFromHtml(html, url);

    return {
      recipe: recipeData,
    };
  } catch (error) {
    // Handle specific error types
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        return {
          error: 'Request timeout - website took too long to respond',
        };
      }
      if (error.message.includes('fetch')) {
        return {
          error: 'Network error - unable to reach website',
        };
      }
    }

    return {
      error: 'Internal server error',
    };
  }
}

/**
 * Scrape recipe data from a given URL using the API route
 */
export async function scrapeRecipeFromUrl(
  url: string
): Promise<RecipeScrapingResult> {
  try {
    const response = await fetch('/api/scrape-recipe', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ url }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      const errorMessage = errorData.error || 'Failed to scrape recipe';

      return {
        error: errorMessage,
      };
    }

    const data = await response.json();
    const recipe = data.recipe || {};

    return {
      recipe,
    };
  } catch {
    return {
      error: 'Network error while scraping recipe',
    };
  }
}

/**
 * Convert scraped recipe data to database format
 */
export function convertScrapedRecipeToDbFormat(
  scrapedRecipe: ScrapedRecipeData,
  originalRecipe: {
    title: string;
    description?: string | null;
    recipe_url?: string | null;
    user_id: string;
  }
) {
  return {
    title: scrapedRecipe.title || originalRecipe.title,
    description: scrapedRecipe.description || originalRecipe.description,
    recipe_url: originalRecipe.recipe_url,
    user_id: originalRecipe.user_id,
    ingredients: scrapedRecipe.ingredients || null,
    instructions: scrapedRecipe.instructions || null,
    prep_time_minutes: scrapedRecipe.prepTime || null,
    cook_time_minutes: scrapedRecipe.cookTime || null,
    total_time_minutes: scrapedRecipe.totalTime || null,
    servings: scrapedRecipe.servings || null,
    difficulty_level: scrapedRecipe.difficulty || null,
    cuisine_type: scrapedRecipe.cuisine || null,
    image_url: scrapedRecipe.image || null,
    scraped_data: scrapedRecipe as Record<string, unknown>,
  };
}

/**
 * Format time in minutes to human readable format
 */
export function formatTime(minutes?: number | null): string {
  if (!minutes) return '';

  if (minutes < 60) {
    return `${minutes} min`;
  }

  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;

  if (remainingMinutes === 0) {
    return `${hours} hr`;
  }

  return `${hours} hr ${remainingMinutes} min`;
}

/**
 * Clean and format ingredient text
 */
export function formatIngredient(ingredient: string): string {
  let formatted = ingredient.trim();

  // Remove numbered list prefixes (like "1. " or "2. ") but not decimal numbers
  formatted = formatted.replace(/^(\d+)\.\s+/, '');

  // Remove dash prefixes
  formatted = formatted.replace(/^-\s*/, '');

  // Fix decimal formatting - round long decimals to 3 decimal places
  formatted = formatted.replace(/\b(\d+\.\d{4,})\b/g, (match) => {
    const number = parseFloat(match);
    if (!isNaN(number)) {
      // Round to 3 decimal places and remove trailing zeros
      return parseFloat(number.toFixed(3)).toString();
    }
    return match;
  });

  return formatted;
}

/**
 * Clean and format instruction text
 */
export function formatInstruction(instruction: string): string {
  const cleaned = instruction.trim().replace(/^\d+\.\s*/, '');
  // Don't add numbering prefix since the UI already provides numbered circles
  return cleaned.startsWith('Step') ? cleaned : cleaned;
}

/**
 * Extract recipe data from HTML content
 */
function extractRecipeFromHtml(
  html: string,
  baseUrl: string
): ScrapedRecipeData {
  const recipe: ScrapedRecipeData = {};

  // First, try to extract JSON-LD structured data
  const jsonLdData = extractJsonLdRecipe(html);
  if (jsonLdData) {
    Object.assign(recipe, jsonLdData);
  }

  // Then try microdata
  if (!recipe.title || !recipe.ingredients) {
    const microdataRecipe = extractMicrodataRecipe(html);
    if (microdataRecipe) {
      Object.assign(recipe, { ...microdataRecipe, ...recipe });
    }
  }

  // Finally, try OpenGraph and meta tags
  if (!recipe.title || !recipe.description || !recipe.image) {
    const metaData = extractMetaData(html, baseUrl);
    Object.assign(recipe, { ...metaData, ...recipe });
  }

  return recipe;
}

function extractJsonLdRecipe(html: string): ScrapedRecipeData | null {
  try {
    // Find all JSON-LD script tags
    const jsonLdRegex =
      /<script[^>]*type=["']application\/ld\+json["'][^>]*>([\s\S]*?)<\/script>/gi;
    let match;

    while ((match = jsonLdRegex.exec(html)) !== null) {
      try {
        const jsonData = JSON.parse(match[1]);

        // Handle both single objects and arrays
        const items = Array.isArray(jsonData) ? jsonData : [jsonData];

        for (const item of items) {
          if (
            item['@type'] === 'Recipe' ||
            (Array.isArray(item['@type']) && item['@type'].includes('Recipe'))
          ) {
            return parseJsonLdRecipe(item);
          }

          // Sometimes recipes are nested in other structures
          if (item['@graph']) {
            for (const graphItem of item['@graph']) {
              if (graphItem['@type'] === 'Recipe') {
                return parseJsonLdRecipe(graphItem);
              }
            }
          }
        }
      } catch {
        // Continue to next JSON-LD block if parsing fails
        continue;
      }
    }
  } catch {
    // Ignore errors and continue
  }

  return null;
}

function parseJsonLdRecipe(data: Record<string, unknown>): ScrapedRecipeData {
  const recipe: ScrapedRecipeData = {};

  if (typeof data.name === 'string') recipe.title = data.name;
  if (typeof data.description === 'string')
    recipe.description = data.description;

  // Parse ingredients
  if (data.recipeIngredient) {
    if (Array.isArray(data.recipeIngredient)) {
      recipe.ingredients = data.recipeIngredient.filter(
        (item): item is string => typeof item === 'string'
      );
    } else if (typeof data.recipeIngredient === 'string') {
      recipe.ingredients = [data.recipeIngredient];
    }
  }

  // Parse instructions
  if (Array.isArray(data.recipeInstructions)) {
    recipe.instructions = parseInstructions(data.recipeInstructions);
  }

  // Parse times (convert ISO 8601 duration to minutes)
  if (typeof data.prepTime === 'string')
    recipe.prepTime = parseDuration(data.prepTime);
  if (typeof data.cookTime === 'string')
    recipe.cookTime = parseDuration(data.cookTime);
  if (typeof data.totalTime === 'string')
    recipe.totalTime = parseDuration(data.totalTime);

  // Parse servings/yield
  if (data.recipeYield) {
    const yield_ = Array.isArray(data.recipeYield)
      ? data.recipeYield[0]
      : data.recipeYield;
    if (typeof yield_ === 'string' || typeof yield_ === 'number') {
      recipe.servings =
        parseInt(yield_.toString().replace(/\D/g, '')) || undefined;
    }
  }

  // Parse image
  if (data.image) {
    const imageData = Array.isArray(data.image) ? data.image[0] : data.image;
    if (typeof imageData === 'string') {
      recipe.image = imageData;
    } else if (
      typeof imageData === 'object' &&
      imageData !== null &&
      'url' in imageData
    ) {
      recipe.image =
        typeof imageData.url === 'string' ? imageData.url : undefined;
    }
  }

  // Parse author
  if (data.author) {
    const authorData = Array.isArray(data.author)
      ? data.author[0]
      : data.author;
    if (typeof authorData === 'string') {
      recipe.author = authorData;
    } else if (
      typeof authorData === 'object' &&
      authorData !== null &&
      'name' in authorData
    ) {
      recipe.author =
        typeof authorData.name === 'string' ? authorData.name : undefined;
    }
  }

  // Parse rating
  if (
    typeof data.aggregateRating === 'object' &&
    data.aggregateRating !== null
  ) {
    const rating = data.aggregateRating as Record<string, unknown>;
    if (
      typeof rating.ratingValue === 'string' ||
      typeof rating.ratingValue === 'number'
    ) {
      recipe.rating = parseFloat(rating.ratingValue.toString());
    }
    if (
      typeof rating.reviewCount === 'string' ||
      typeof rating.reviewCount === 'number'
    ) {
      recipe.reviewCount = parseInt(rating.reviewCount.toString()) || undefined;
    }
  }

  // Parse cuisine
  if (data.recipeCuisine) {
    if (Array.isArray(data.recipeCuisine)) {
      const cuisines = data.recipeCuisine.filter(
        (item): item is string => typeof item === 'string'
      );
      recipe.cuisine = cuisines.join(', ');
    } else if (typeof data.recipeCuisine === 'string') {
      recipe.cuisine = data.recipeCuisine;
    }
  }

  return recipe;
}

function parseInstructions(instructions: unknown[]): string[] {
  return instructions
    .map((instruction) => {
      if (typeof instruction === 'string') {
        return instruction;
      }
      if (typeof instruction === 'object' && instruction !== null) {
        const obj = instruction as Record<string, unknown>;
        if (typeof obj.text === 'string') {
          return obj.text;
        }
        if (typeof obj.name === 'string') {
          return obj.name;
        }
      }
      return String(instruction);
    })
    .filter((text): text is string => Boolean(text));
}

function parseDuration(duration: string): number | undefined {
  if (!duration) return undefined;

  // Handle ISO 8601 duration format (PT15M, PT1H30M, etc.)
  const iso8601Match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?/);
  if (iso8601Match) {
    const hours = parseInt(iso8601Match[1] || '0');
    const minutes = parseInt(iso8601Match[2] || '0');
    return hours * 60 + minutes;
  }

  // Handle simple number formats
  const numberMatch = duration.match(/(\d+)/);
  if (numberMatch) {
    return parseInt(numberMatch[1]);
  }

  return undefined;
}

function extractMicrodataRecipe(html: string): ScrapedRecipeData | null {
  // This is a simplified microdata parser
  // In a production app, you might want to use a proper microdata parsing library
  const recipe: ScrapedRecipeData = {};

  // Look for recipe microdata
  const recipeMatch = html.match(
    /<[^>]*itemtype=["'][^"']*schema\.org\/Recipe["'][^>]*>/i
  );
  if (!recipeMatch) return null;

  // Extract title
  const titleMatch = html.match(/<[^>]*itemprop=["']name["'][^>]*>([^<]+)</i);
  if (titleMatch) recipe.title = titleMatch[1].trim();

  // Extract description
  const descMatch = html.match(
    /<[^>]*itemprop=["']description["'][^>]*>([^<]+)</i
  );
  if (descMatch) recipe.description = descMatch[1].trim();

  return Object.keys(recipe).length > 0 ? recipe : null;
}

function extractMetaData(html: string, baseUrl: string): ScrapedRecipeData {
  const recipe: ScrapedRecipeData = {};

  // Extract Open Graph data
  const ogTitleMatch = html.match(
    /<meta[^>]*property=["']og:title["'][^>]*content=["']([^"']+)["']/i
  );
  if (ogTitleMatch) recipe.title = ogTitleMatch[1];

  const ogDescMatch = html.match(
    /<meta[^>]*property=["']og:description["'][^>]*content=["']([^"']+)["']/i
  );
  if (ogDescMatch) recipe.description = ogDescMatch[1];

  const ogImageMatch = html.match(
    /<meta[^>]*property=["']og:image["'][^>]*content=["']([^"']+)["']/i
  );
  if (ogImageMatch) {
    recipe.image = resolveUrl(ogImageMatch[1], baseUrl);
  }

  // Extract title tag if no OG title
  if (!recipe.title) {
    const titleMatch = html.match(/<title[^>]*>([^<]+)</i);
    if (titleMatch) recipe.title = titleMatch[1].trim();
  }

  // Extract meta description if no OG description
  if (!recipe.description) {
    const metaDescMatch = html.match(
      /<meta[^>]*name=["']description["'][^>]*content=["']([^"']+)["']/i
    );
    if (metaDescMatch) recipe.description = metaDescMatch[1];
  }

  return recipe;
}

function resolveUrl(url: string, baseUrl: string): string | undefined {
  try {
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }
    if (url.startsWith('//')) {
      return `https:${url}`;
    }
    if (url.startsWith('/')) {
      const base = new URL(baseUrl);
      return `${base.protocol}//${base.host}${url}`;
    }
    return new URL(url, baseUrl).href;
  } catch {
    return undefined;
  }
}
