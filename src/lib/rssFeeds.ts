import {
  supabase,
  RssFeed,
  RssFeedInsert,
  RssFeedUpdate,
  RssImportLog,
} from './supabase';

/**
 * Fetch all RSS feeds for the current user
 */
export async function getRssFeeds(): Promise<RssFeed[]> {
  const { data, error } = await supabase
    .from('rss_feeds')
    .select('*')
    .order('created_at', { ascending: false });

  if (error) {
    throw new Error('Failed to fetch RSS feeds');
  }

  return data || [];
}

/**
 * Fetch a single RSS feed by ID
 */
export async function getRssFeed(id: number): Promise<RssFeed | null> {
  const { data, error } = await supabase
    .from('rss_feeds')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      // No rows returned
      return null;
    }
    throw new Error('Failed to fetch RSS feed');
  }

  return data;
}

/**
 * Create a new RSS feed
 */
export async function createRssFeed(feed: RssFeedInsert): Promise<RssFeed> {
  const { data, error } = await supabase
    .from('rss_feeds')
    .insert(feed)
    .select()
    .single();

  if (error) {
    if (error.code === '23505') {
      // Unique constraint violation
      throw new Error('RSS feed URL already exists');
    }
    // Include the actual error details for debugging
    throw new Error(
      `Failed to create RSS feed: ${error.message} (Code: ${error.code})`
    );
  }

  return data;
}

/**
 * Update an existing RSS feed
 */
export async function updateRssFeed(
  id: number,
  updates: RssFeedUpdate
): Promise<RssFeed> {
  const { data, error } = await supabase
    .from('rss_feeds')
    .update(updates)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    if (error.code === '23505') {
      // Unique constraint violation
      throw new Error('RSS feed URL already exists');
    }
    throw new Error('Failed to update RSS feed');
  }

  return data;
}

/**
 * Delete an RSS feed
 */
export async function deleteRssFeed(id: number): Promise<void> {
  const { error } = await supabase.from('rss_feeds').delete().eq('id', id);

  if (error) {
    throw new Error('Failed to delete RSS feed');
  }
}

/**
 * Toggle RSS feed active status
 */
export async function toggleRssFeedStatus(
  id: number,
  isActive: boolean
): Promise<RssFeed> {
  return updateRssFeed(id, { is_active: isActive });
}

/**
 * Get RSS import logs for a feed
 */
export async function getRssImportLogs(
  feedId: number,
  limit = 10
): Promise<RssImportLog[]> {
  const { data, error } = await supabase
    .from('rss_import_logs')
    .select('*')
    .eq('rss_feed_id', feedId)
    .order('started_at', { ascending: false })
    .limit(limit);

  if (error) {
    throw new Error('Failed to fetch import logs');
  }

  return data || [];
}

/**
 * Get all RSS import logs for the current user's feeds
 */
export async function getAllRssImportLogs(
  limit = 50
): Promise<(RssImportLog & { feed_name: string })[]> {
  const { data, error } = await supabase
    .from('rss_import_logs')
    .select(
      `
      *,
      rss_feeds!inner(name)
    `
    )
    .order('started_at', { ascending: false })
    .limit(limit);

  if (error) {
    throw new Error('Failed to fetch import logs');
  }

  return (data || []).map((log) => ({
    ...log,
    feed_name: (log.rss_feeds as unknown as { name: string }).name,
  }));
}

/**
 * Create an import log entry
 */
export async function createImportLog(
  log: Omit<RssImportLog, 'id' | 'created_at'>
): Promise<RssImportLog> {
  const { data, error } = await supabase
    .from('rss_import_logs')
    .insert(log)
    .select()
    .single();

  if (error) {
    throw new Error('Failed to create import log');
  }

  return data;
}

/**
 * Update an import log entry
 */
export async function updateImportLog(
  id: number,
  updates: Partial<
    Pick<
      RssImportLog,
      | 'status'
      | 'items_found'
      | 'items_imported'
      | 'items_skipped'
      | 'error_message'
      | 'completed_at'
    >
  >
): Promise<RssImportLog> {
  const { data, error } = await supabase
    .from('rss_import_logs')
    .update(updates)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    throw new Error('Failed to update import log');
  }

  return data;
}

/**
 * Update RSS feed statistics after import
 */
export async function updateRssFeedStats(
  feedId: number,
  stats: {
    lastCheckedAt: string;
    lastSuccessfulImportAt?: string;
    importCount?: number;
    errorCount?: number;
    lastError?: string | null;
  }
): Promise<RssFeed> {
  const updates: RssFeedUpdate = {
    last_checked_at: stats.lastCheckedAt,
  };

  if (stats.lastSuccessfulImportAt) {
    updates.last_successful_import_at = stats.lastSuccessfulImportAt;
  }

  if (stats.importCount !== undefined) {
    updates.import_count = stats.importCount;
  }

  if (stats.errorCount !== undefined) {
    updates.error_count = stats.errorCount;
  }

  if (stats.lastError !== undefined) {
    updates.last_error = stats.lastError;
  }

  return updateRssFeed(feedId, updates);
}

/**
 * Get active RSS feeds that need checking
 */
export async function getActiveFeedsForImport(): Promise<RssFeed[]> {
  const { data, error } = await supabase
    .from('rss_feeds')
    .select('*')
    .eq('is_active', true)
    .order('last_checked_at', { ascending: true, nullsFirst: true });

  if (error) {
    throw new Error('Failed to fetch active RSS feeds');
  }

  return data || [];
}

/**
 * Validate RSS feed URL format
 */
export function validateRssFeedUrl(url: string): boolean {
  try {
    const parsedUrl = new URL(url);
    return parsedUrl.protocol === 'http:' || parsedUrl.protocol === 'https:';
  } catch {
    return false;
  }
}

/**
 * Extract domain from RSS feed URL for display
 */
export function extractDomainFromUrl(url: string): string {
  try {
    const parsedUrl = new URL(url);
    return parsedUrl.hostname;
  } catch {
    return url;
  }
}
