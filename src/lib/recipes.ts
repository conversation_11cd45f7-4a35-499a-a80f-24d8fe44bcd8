import {
  supabase,
  Recipe,
  RecipeInsert,
  RecipeUpdate,
  RecipeWithRssFeed,
  RecipeWithFolders,
} from './supabase';
import type { SupabaseClient } from '@supabase/supabase-js';

/**
 * Fetch all recipes from the database
 */
export async function getRecipes(): Promise<RecipeWithRssFeed[]> {
  const { data, error } = await supabase
    .from('recipes')
    .select(
      `
      *,
      rss_feeds (
        id,
        name,
        url,
        description
      )
    `
    )
    .order('updated_at', { ascending: false })
    .limit(500);

  if (error) {
    throw new Error('Failed to fetch recipes');
  }

  return (data || []) as RecipeWithRssFeed[];
}

/**
 * Fetch a single recipe by ID
 */
export async function getRecipe(id: number): Promise<RecipeWithRssFeed | null> {
  const { data, error } = await supabase
    .from('recipes')
    .select(
      `
      *,
      rss_feeds (
        id,
        name,
        url,
        description
      )
    `
    )
    .eq('id', id)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      // No rows returned
      return null;
    }
    throw new Error('Failed to fetch recipe');
  }

  return data as RecipeWithRssFeed;
}

/**
 * Create a new recipe
 * @param recipe - Recipe data to insert
 * @param client - Optional Supabase client to use (defaults to browser client)
 */
export async function createRecipe(
  recipe: RecipeInsert,
  client?: SupabaseClient
): Promise<Recipe> {
  const supabaseClient = client || supabase;

  const { data, error } = await supabaseClient
    .from('recipes')
    .insert(recipe)
    .select()
    .single();

  if (error) {
    // Include detailed error information in the thrown error
    const errorDetails = {
      code: error.code,
      message: error.message,
      details: error.details,
      hint: error.hint,
    };

    throw new Error(
      `Failed to create recipe: ${error.message} (Code: ${error.code}). Details: ${JSON.stringify(errorDetails)}`
    );
  }

  return data;
}

/**
 * Update an existing recipe
 */
export async function updateRecipe(
  id: number,
  updates: RecipeUpdate
): Promise<Recipe> {
  const { data, error } = await supabase
    .from('recipes')
    .update(updates)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    throw new Error('Failed to update recipe');
  }

  return data;
}

/**
 * Delete a recipe
 */
export async function deleteRecipe(id: number): Promise<void> {
  const { error } = await supabase.from('recipes').delete().eq('id', id);

  if (error) {
    throw new Error('Failed to delete recipe');
  }
}

/**
 * Search recipes by title
 */
export async function searchRecipes(
  query: string
): Promise<RecipeWithRssFeed[]> {
  const { data, error } = await supabase
    .from('recipes')
    .select(
      `
      *,
      rss_feeds (
        id,
        name,
        url,
        description
      )
    `
    )
    .ilike('title', `%${query}%`)
    .order('created_at', { ascending: false });

  if (error) {
    throw new Error('Failed to search recipes');
  }

  return (data || []) as RecipeWithRssFeed[];
}

/**
 * Toggle favorite status of a recipe
 */
export async function toggleRecipeFavorite(id: number): Promise<Recipe> {
  // First get the current favorite status
  const { data: currentRecipe, error: fetchError } = await supabase
    .from('recipes')
    .select('is_favorite')
    .eq('id', id)
    .single();

  if (fetchError) {
    throw new Error('Failed to fetch recipe favorite status');
  }

  // Toggle the favorite status
  const newFavoriteStatus = !currentRecipe.is_favorite;

  const { data, error } = await supabase
    .from('recipes')
    .update({ is_favorite: newFavoriteStatus })
    .eq('id', id)
    .select()
    .single();

  if (error) {
    throw new Error('Failed to update recipe favorite status');
  }

  return data;
}

/**
 * Get only favorite recipes
 */
export async function getFavoriteRecipes(): Promise<RecipeWithRssFeed[]> {
  const { data, error } = await supabase
    .from('recipes')
    .select(
      `
      *,
      rss_feeds (
        id,
        name,
        url,
        description
      )
    `
    )
    .eq('is_favorite', true)
    .order('updated_at', { ascending: false });

  if (error) {
    throw new Error('Failed to fetch favorite recipes');
  }

  return (data || []) as RecipeWithRssFeed[];
}

/**
 * Get recipes with their folder associations
 */
export async function getRecipesWithFolders(): Promise<RecipeWithFolders[]> {
  const { data, error } = await supabase
    .from('recipes')
    .select(
      `
      *,
      rss_feeds (
        id,
        name,
        url,
        description
      ),
      recipe_folders (
        folders (*)
      )
    `
    )
    .order('updated_at', { ascending: false })
    .limit(500);

  if (error) {
    throw new Error('Failed to fetch recipes with folders');
  }

  // Transform the data to flatten folder relationships
  type RecipeWithFolderRelations = RecipeWithRssFeed & {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    recipe_folders?: Array<{ folders: any }>;
  };

  return (data || []).map((recipe: RecipeWithFolderRelations) => ({
    ...recipe,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    folders: recipe.recipe_folders?.map((rf: { folders: any }) => rf.folders).filter(Boolean) || [],
  })) as RecipeWithFolders[];
}
