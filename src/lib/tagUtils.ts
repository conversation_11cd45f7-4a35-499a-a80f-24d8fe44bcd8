import { Recipe } from './supabase';

export const INGREDIENT_SUGGESTIONS_LIMIT = 50;
/**
 * Extract key ingredients from a recipe's ingredients array for display as tags
 * This function tries to identify the main ingredients by filtering out quantities and common words
 */
export function extractKeyIngredients(
  ingredients: string[] | null,
  maxCount: number = 3
): string[] {
  if (!ingredients || ingredients.length === 0) {
    return [];
  }

  // Common words to filter out when extracting key ingredients
  const commonWords = new Set([
    'cup',
    'cups',
    'tablespoon',
    'tablespoons',
    'tbsp',
    'teaspoon',
    'teaspoons',
    'tsp',
    'pound',
    'pounds',
    'lb',
    'lbs',
    'ounce',
    'ounces',
    'oz',
    'gram',
    'grams',
    'g',
    'kilogram',
    'kilograms',
    'kg',
    'liter',
    'liters',
    'l',
    'milliliter',
    'milliliters',
    'ml',
    'large',
    'medium',
    'small',
    'fresh',
    'dried',
    'chopped',
    'diced',
    'sliced',
    'minced',
    'grated',
    'shredded',
    'crushed',
    'ground',
    'whole',
    'half',
    'quarter',
    'pinch',
    'dash',
    'splash',
    'to',
    'taste',
    'or',
    'and',
    'the',
    'a',
    'an',
    'of',
    'for',
    'with',
    'without',
    'plus',
    'extra',
    'optional',
    'garnish',
    'serving',
    'serve',
    'prepared',
    'cooked',
    'raw',
    'canned',
    'frozen',
    'thawed',
    'room',
    'temperature',
    'cold',
    'hot',
    'warm',
    'cool',
    'refrigerated',
    'softened',
    'melted',
    'beaten',
    'whisked',
    'mixed',
    'combined',
    'separated',
    'divided',
    'reserved',
    'set',
    'aside',
  ]);

  // Numbers and fractions to filter out
  const numberPattern = /^\d+(\.\d+)?$|^\d+\/\d+$|^\d+\s+\d+\/\d+$/;

  const keyIngredients: string[] = [];

  for (const ingredient of ingredients) {
    if (keyIngredients.length >= maxCount) break;

    // Clean and split the ingredient string
    const words = ingredient
      .toLowerCase()
      .replace(/[(),]/g, '') // Remove parentheses and commas
      .split(/\s+/)
      .filter((word) => word.length > 2) // Filter out very short words
      .filter((word) => !commonWords.has(word)) // Filter out common words
      .filter((word) => !numberPattern.test(word)); // Filter out numbers

    // Take the first meaningful word(s) as the key ingredient
    if (words.length > 0) {
      // For compound ingredients, take up to 2 words
      const keyIngredient = words.slice(0, 2).join(' ');

      // Capitalize first letter
      const capitalizedIngredient =
        keyIngredient.charAt(0).toUpperCase() + keyIngredient.slice(1);

      // Avoid duplicates
      if (
        !keyIngredients.some(
          (existing) =>
            existing
              .toLowerCase()
              .includes(capitalizedIngredient.toLowerCase()) ||
            capitalizedIngredient.toLowerCase().includes(existing.toLowerCase())
        )
      ) {
        keyIngredients.push(capitalizedIngredient);
      }
    }
  }

  return keyIngredients;
}

/**
 * Get all unique cuisine types from a list of recipes
 */
export function getUniqueCuisineTypes(recipes: Recipe[]): string[] {
  const cuisineTypes = new Set<string>();

  recipes.forEach((recipe) => {
    if (recipe.cuisine_type && recipe.cuisine_type.trim()) {
      cuisineTypes.add(recipe.cuisine_type.trim());
    }
  });

  return Array.from(cuisineTypes).sort();
}

/**
 * Get cuisine types with their counts, ordered by count (descending)
 */
export function getCuisineTypesWithCounts(
  recipes: Recipe[]
): Array<{ name: string; count: number }> {
  const cuisineCounts = new Map<string, number>();

  recipes.forEach((recipe) => {
    if (recipe.cuisine_type && recipe.cuisine_type.trim()) {
      const cuisine = recipe.cuisine_type.trim();
      cuisineCounts.set(cuisine, (cuisineCounts.get(cuisine) || 0) + 1);
    }
  });

  return Array.from(cuisineCounts.entries())
    .map(([name, count]) => ({ name, count }))
    .sort((a, b) => b.count - a.count); // Sort by count descending
}

/**
 * Get ingredients with their counts, ordered by count (descending)
 */
export function getIngredientsWithCounts(
  recipes: Recipe[]
): Array<{ name: string; count: number }> {
  const ingredientCounts = new Map<string, number>();

  recipes.forEach((recipe) => {
    if (recipe.ingredients) {
      const keyIngredients = extractKeyIngredients(recipe.ingredients, 10); // Get more for counting
      keyIngredients.forEach((ingredient) => {
        ingredientCounts.set(
          ingredient,
          (ingredientCounts.get(ingredient) || 0) + 1
        );
      });
    }
  });

  return Array.from(ingredientCounts.entries())
    .map(([name, count]) => ({ name, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, INGREDIENT_SUGGESTIONS_LIMIT);
}

/**
 * Get all unique ingredients from a list of recipes (for autocomplete/suggestions)
 * Returns top 30 ingredients sorted by frequency
 */
export function getAllUniqueIngredients(recipes: Recipe[]): string[] {
  const ingredientsWithCounts = getIngredientsWithCounts(recipes);

  return ingredientsWithCounts.map(({ name }) => name);
}

/**
 * Filter recipes based on search query, cuisine type, and ingredients
 */
export function filterRecipes(
  recipes: Recipe[],
  searchQuery: string,
  selectedCuisines: string[] = [],
  selectedIngredients: string[] = []
): Recipe[] {
  return recipes.filter((recipe) => {
    // Text search in title and description
    const matchesSearch =
      !searchQuery.trim() ||
      recipe.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (recipe.description &&
        recipe.description.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (recipe.ingredients &&
        recipe.ingredients.some((ingredient) =>
          ingredient.toLowerCase().includes(searchQuery.toLowerCase())
        ));
    // Cuisine filter
    const matchesCuisine =
      selectedCuisines.length === 0 ||
      (recipe.cuisine_type && selectedCuisines.includes(recipe.cuisine_type));

    // Ingredient filter
    const matchesIngredients =
      selectedIngredients.length === 0 ||
      (recipe.ingredients &&
        selectedIngredients.some((selectedIngredient) =>
          recipe.ingredients!.some((recipeIngredient) =>
            recipeIngredient
              .toLowerCase()
              .includes(selectedIngredient.toLowerCase())
          )
        ));

    return matchesSearch && matchesCuisine && matchesIngredients;
  });
}

/**
 * Common cuisine types for dropdown options
 */
export const COMMON_CUISINE_TYPES = [
  'American',
  'Asian',
  'Chinese',
  'French',
  'Indian',
  'Italian',
  'Japanese',
  'Korean',
  'Mediterranean',
  'Mexican',
  'Thai',
  'Vietnamese',
  'Western',
  'Other',
];
