import {
  supabase,
  Folder,
  FolderInsert,
  FolderUpdate,
  FolderWithRecipeCount,
  RecipeFolderInsert,
} from './supabase';
import type { SupabaseClient } from '@supabase/supabase-js';

/**
 * Fetch all folders for the current user
 * @param client - Optional Supabase client to use (defaults to browser client)
 */
export async function getFolders(client?: SupabaseClient): Promise<FolderWithRecipeCount[]> {
  const supabaseClient = client || supabase;

  const { data: folders, error } = await supabaseClient
    .from('folders')
    .select('*')
    .order('name', { ascending: true });

  if (error) {
    throw new Error('Failed to fetch folders');
  }

  // Get recipe counts for each folder
  const foldersWithCounts = await Promise.all(
    (folders || []).map(async (folder) => {
      const { count } = await supabaseClient
        .from('recipe_folders')
        .select('*', { count: 'exact', head: true })
        .eq('folder_id', folder.id);

      return {
        ...folder,
        recipe_count: count || 0,
      };
    })
  );

  return foldersWithCounts;
}

/**
 * Create a new folder
 * @param name - Folder name
 * @param client - Optional Supabase client to use (defaults to browser client)
 */
export async function createFolder(
  name: string,
  client?: SupabaseClient
): Promise<Folder> {
  const supabaseClient = client || supabase;

  const {
    data: { user },
    error: authError,
  } = await supabaseClient.auth.getUser();

  if (authError || !user) {
    throw new Error('User not authenticated');
  }

  const folderData: FolderInsert = {
    name: name.trim(),
    user_id: user.id,
  };

  const { data, error } = await supabaseClient
    .from('folders')
    .insert(folderData)
    .select()
    .single();

  if (error) {
    if (error.code === '23505') {
      throw new Error('A folder with this name already exists');
    }
    throw new Error('Failed to create folder');
  }

  return data;
}

/**
 * Update a folder's name
 * @param id - Folder ID
 * @param updates - Updates to apply
 * @param client - Optional Supabase client to use (defaults to browser client)
 */
export async function updateFolder(
  id: number,
  updates: FolderUpdate,
  client?: SupabaseClient
): Promise<Folder> {
  const supabaseClient = client || supabase;

  const { data, error } = await supabaseClient
    .from('folders')
    .update(updates)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    if (error.code === '23505') {
      throw new Error('A folder with this name already exists');
    }
    throw new Error('Failed to update folder');
  }

  return data;
}

/**
 * Delete a folder and all its recipe associations
 * @param id - Folder ID
 * @param client - Optional Supabase client to use (defaults to browser client)
 */
export async function deleteFolder(id: number, client?: SupabaseClient): Promise<void> {
  const supabaseClient = client || supabase;

  const { error } = await supabaseClient
    .from('folders')
    .delete()
    .eq('id', id);

  if (error) {
    throw new Error('Failed to delete folder');
  }
}

/**
 * Get recipes in a specific folder
 * @param folderId - Folder ID
 * @param client - Optional Supabase client to use (defaults to browser client)
 */
export async function getRecipesInFolder(folderId: number, client?: SupabaseClient) {
  const supabaseClient = client || supabase;

  const { data, error } = await supabaseClient
    .from('recipe_folders')
    .select(`
      recipes (
        *,
        rss_feeds (
          id,
          name,
          url,
          description
        )
      )
    `)
    .eq('folder_id', folderId);

  if (error) {
    throw new Error('Failed to fetch recipes in folder');
  }

  return (data || []).map(item => item.recipes).filter(Boolean);
}

/**
 * Add recipes to a folder
 * @param folderId - Folder ID
 * @param recipeIds - Array of recipe IDs
 * @param client - Optional Supabase client to use (defaults to browser client)
 */
export async function addRecipesToFolder(
  folderId: number,
  recipeIds: number[],
  client?: SupabaseClient
): Promise<void> {
  const supabaseClient = client || supabase;

  const insertData: RecipeFolderInsert[] = recipeIds.map(recipeId => ({
    recipe_id: recipeId,
    folder_id: folderId,
  }));

  const { error } = await supabaseClient
    .from('recipe_folders')
    .insert(insertData);

  if (error) {
    // Ignore duplicate key errors (recipe already in folder)
    if (error.code !== '23505') {
      throw new Error('Failed to add recipes to folder');
    }
  }
}

/**
 * Remove recipes from a folder
 * @param folderId - Folder ID
 * @param recipeIds - Array of recipe IDs
 * @param client - Optional Supabase client to use (defaults to browser client)
 */
export async function removeRecipesFromFolder(
  folderId: number,
  recipeIds: number[],
  client?: SupabaseClient
): Promise<void> {
  const supabaseClient = client || supabase;

  const { error } = await supabaseClient
    .from('recipe_folders')
    .delete()
    .eq('folder_id', folderId)
    .in('recipe_id', recipeIds);

  if (error) {
    throw new Error('Failed to remove recipes from folder');
  }
}

/**
 * Remove a recipe from all folders
 */
export async function removeRecipeFromAllFolders(recipeId: number): Promise<void> {
  const { error } = await supabase
    .from('recipe_folders')
    .delete()
    .eq('recipe_id', recipeId);

  if (error) {
    throw new Error('Failed to remove recipe from folders');
  }
}

/**
 * Get folders that contain a specific recipe
 */
export async function getFoldersForRecipe(recipeId: number): Promise<Folder[]> {
  const { data, error } = await supabase
    .from('recipe_folders')
    .select(`
      folders (*)
    `)
    .eq('recipe_id', recipeId);

  if (error) {
    throw new Error('Failed to fetch folders for recipe');
  }

  // Type assertion for the Supabase response structure
  type RecipeFolderWithFolder = {
    folders: Folder;
  };

  return ((data as unknown) as RecipeFolderWithFolder[] || [])
    .map(item => item.folders)
    .filter(Boolean);
}
