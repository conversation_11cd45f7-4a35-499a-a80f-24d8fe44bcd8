import { decodeHtmlEntities } from '../utils';

describe('decodeHtmlEntities', () => {
  it('should decode common HTML entities', () => {
    const input =
      'Recipe video above. I don&#39;t harvest and I don&#39;t live in the country. I just didn&#39;t think &quot;Root Vegetable Soup&quot; did this recipe justice - so I got creative to get your attention!😅';
    const expected =
      "Recipe video above. I don't harvest and I don't live in the country. I just didn't think \"Root Vegetable Soup\" did this recipe justice - so I got creative to get your attention!😅";

    expect(decodeHtmlEntities(input)).toBe(expected);
  });

  it('should decode various HTML entities', () => {
    expect(decodeHtmlEntities('&amp;')).toBe('&');
    expect(decodeHtmlEntities('&lt;')).toBe('<');
    expect(decodeHtmlEntities('&gt;')).toBe('>');
    expect(decodeHtmlEntities('&quot;')).toBe('"');
    expect(decodeHtmlEntities('&#39;')).toBe("'");
    expect(decodeHtmlEntities('&nbsp;')).toBe(' ');
    expect(decodeHtmlEntities('&#x27;')).toBe("'");
    expect(decodeHtmlEntities('&#x2F;')).toBe('/');
  });

  it('should handle empty or null input', () => {
    expect(decodeHtmlEntities('')).toBe('');
  });

  it('should handle text without entities', () => {
    const input = 'This is a normal text without any entities';
    expect(decodeHtmlEntities(input)).toBe(input);
  });

  it('should handle multiple occurrences of the same entity', () => {
    const input = 'I don&#39;t think it&#39;s right';
    const expected = "I don't think it's right";
    expect(decodeHtmlEntities(input)).toBe(expected);
  });

  it('should handle recipe title with &amp; entity', () => {
    const input = 'Banana Oat Pancakes (Fast, Easy &amp; Gluten-free!)';
    const expected = 'Banana Oat Pancakes (Fast, Easy & Gluten-free!)';
    expect(decodeHtmlEntities(input)).toBe(expected);
  });
});
