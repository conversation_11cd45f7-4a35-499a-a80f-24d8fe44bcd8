import { extractKeyIngredients, getUniqueCuisineTypes, filterRecipes } from '../tagUtils';
import { Recipe } from '../supabase';

describe('tagUtils', () => {
  const mockRecipes: Recipe[] = [
    {
      id: 1,
      title: 'Chicken Curry',
      description: 'Spicy chicken curry',
      image_url: null,
      recipe_url: null,
      user_id: 'user1',
      created_at: '2024-01-01',
      updated_at: '2024-01-01',
      rss_feed_id: null,
      rss_item_guid: null,
      ingredients: ['2 lbs chicken breast', '1 cup coconut milk', '2 tbsp curry powder'],
      instructions: null,
      prep_time_minutes: null,
      cook_time_minutes: null,
      total_time_minutes: null,
      servings: null,
      difficulty_level: null,
      cuisine_type: 'Indian',
      scraped_data: null,
    },
    {
      id: 2,
      title: 'Pasta Carbonara',
      description: 'Classic Italian pasta',
      image_url: null,
      recipe_url: null,
      user_id: 'user1',
      created_at: '2024-01-01',
      updated_at: '2024-01-01',
      rss_feed_id: null,
      rss_item_guid: null,
      ingredients: ['1 lb spaghetti', '4 eggs', '1 cup parmesan cheese', '8 oz pancetta'],
      instructions: null,
      prep_time_minutes: null,
      cook_time_minutes: null,
      total_time_minutes: null,
      servings: null,
      difficulty_level: null,
      cuisine_type: 'Italian',
      scraped_data: null,
    },
  ];

  describe('extractKeyIngredients', () => {
    it('should extract key ingredients from ingredient list', () => {
      const ingredients = ['2 lbs chicken breast', '1 cup coconut milk', '2 tbsp curry powder'];
      const result = extractKeyIngredients(ingredients, 3);
      
      expect(result).toContain('Chicken breast');
      expect(result).toContain('Coconut milk');
      expect(result).toContain('Curry powder');
    });

    it('should return empty array for null ingredients', () => {
      const result = extractKeyIngredients(null, 3);
      expect(result).toEqual([]);
    });

    it('should limit the number of ingredients returned', () => {
      const ingredients = ['chicken', 'rice', 'onion', 'garlic', 'ginger'];
      const result = extractKeyIngredients(ingredients, 2);
      expect(result.length).toBeLessThanOrEqual(2);
    });
  });

  describe('getUniqueCuisineTypes', () => {
    it('should return unique cuisine types from recipes', () => {
      const result = getUniqueCuisineTypes(mockRecipes);
      expect(result).toEqual(['Indian', 'Italian']);
    });

    it('should handle recipes without cuisine types', () => {
      const recipesWithoutCuisine = mockRecipes.map(recipe => ({
        ...recipe,
        cuisine_type: null,
      }));
      const result = getUniqueCuisineTypes(recipesWithoutCuisine);
      expect(result).toEqual([]);
    });
  });

  describe('filterRecipes', () => {
    it('should filter recipes by search query', () => {
      const result = filterRecipes(mockRecipes, 'chicken', [], []);
      expect(result).toHaveLength(1);
      expect(result[0].title).toBe('Chicken Curry');
    });

    it('should filter recipes by cuisine type', () => {
      const result = filterRecipes(mockRecipes, '', ['Italian'], []);
      expect(result).toHaveLength(1);
      expect(result[0].title).toBe('Pasta Carbonara');
    });

    it('should filter recipes by ingredients', () => {
      const result = filterRecipes(mockRecipes, '', [], ['chicken']);
      expect(result).toHaveLength(1);
      expect(result[0].title).toBe('Chicken Curry');
    });

    it('should return all recipes when no filters are applied', () => {
      const result = filterRecipes(mockRecipes, '', [], []);
      expect(result).toHaveLength(2);
    });

    it('should combine multiple filters', () => {
      const result = filterRecipes(mockRecipes, 'curry', ['Indian'], ['chicken']);
      expect(result).toHaveLength(1);
      expect(result[0].title).toBe('Chicken Curry');
    });
  });
});
