import { formatIngredient, formatInstruction, formatTime } from '../recipeScraper';

describe('recipeScraper formatting functions', () => {
  describe('formatIngredient', () => {
    it('removes numbered prefixes', () => {
      expect(formatIngredient('1. 2 cups flour')).toBe('2 cups flour');
      expect(formatIngredient('2. 1 tsp salt')).toBe('1 tsp salt');
    });

    it('removes dash prefixes', () => {
      expect(formatIngredient('- 2 cups flour')).toBe('2 cups flour');
      expect(formatIngredient('- 1 tsp salt')).toBe('1 tsp salt');
    });

    it('formats long decimals correctly', () => {
      expect(formatIngredient('0.66666668653488 cups sugar')).toBe('0.667 cups sugar');
      expect(formatIngredient('1.33333333333 tsp vanilla')).toBe('1.333 tsp vanilla');
      expect(formatIngredient('2.50000000000 cups milk')).toBe('2.5 cups milk');
    });

    it('leaves normal decimals unchanged', () => {
      expect(formatIngredient('0.5 cups butter')).toBe('0.5 cups butter');
      expect(formatIngredient('1.25 tsp baking powder')).toBe('1.25 tsp baking powder');
    });

    it('handles ingredients without numbers', () => {
      expect(formatIngredient('Salt to taste')).toBe('Salt to taste');
      expect(formatIngredient('Fresh herbs')).toBe('Fresh herbs');
    });

    it('trims whitespace', () => {
      expect(formatIngredient('  2 cups flour  ')).toBe('2 cups flour');
      expect(formatIngredient('\t1 tsp salt\n')).toBe('1 tsp salt');
    });
  });

  describe('formatInstruction', () => {
    it('removes numbered prefixes', () => {
      expect(formatInstruction('1. Mix the ingredients')).toBe('Mix the ingredients');
      expect(formatInstruction('2. Bake for 30 minutes')).toBe('Bake for 30 minutes');
    });

    it('preserves Step prefixes', () => {
      expect(formatInstruction('Step 1: Mix ingredients')).toBe('Step 1: Mix ingredients');
      expect(formatInstruction('Step 2: Bake')).toBe('Step 2: Bake');
    });

    it('does not add numbering prefix', () => {
      expect(formatInstruction('Mix the ingredients')).toBe('Mix the ingredients');
      expect(formatInstruction('Bake for 30 minutes')).toBe('Bake for 30 minutes');
    });

    it('trims whitespace', () => {
      expect(formatInstruction('  Mix ingredients  ')).toBe('Mix ingredients');
      expect(formatInstruction('\tBake for 30 minutes\n')).toBe('Bake for 30 minutes');
    });
  });

  describe('formatTime', () => {
    it('formats minutes correctly', () => {
      expect(formatTime(15)).toBe('15 min');
      expect(formatTime(45)).toBe('45 min');
    });

    it('formats hours correctly', () => {
      expect(formatTime(60)).toBe('1 hr');
      expect(formatTime(120)).toBe('2 hr');
    });

    it('formats hours and minutes correctly', () => {
      expect(formatTime(75)).toBe('1 hr 15 min');
      expect(formatTime(150)).toBe('2 hr 30 min');
    });

    it('handles null and undefined', () => {
      expect(formatTime(null)).toBe('');
      expect(formatTime(undefined)).toBe('');
    });

    it('handles zero', () => {
      expect(formatTime(0)).toBe('');
    });
  });
});
