import { createBrowserClient } from '@supabase/ssr';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createBrowserClient(supabaseUrl, supabaseAnonKey);

// Database types
export interface Database {
  public: {
    Tables: {
      recipes: {
        Row: {
          id: number;
          title: string;
          description: string | null;
          image_url: string | null;
          recipe_url: string | null;
          user_id: string;
          created_at: string;
          updated_at: string;
          rss_feed_id: number | null;
          rss_item_guid: string | null;
          ingredients: string[] | null;
          instructions: string[] | null;
          prep_time_minutes: number | null;
          cook_time_minutes: number | null;
          total_time_minutes: number | null;
          servings: number | null;
          difficulty_level: string | null;
          cuisine_type: string | null;
          scraped_data: Record<string, unknown> | null;
          is_favorite: boolean | null;
        };
        Insert: {
          id?: number;
          title: string;
          description?: string | null;
          image_url?: string | null;
          recipe_url?: string | null;
          user_id: string;
          created_at?: string;
          updated_at?: string;
          rss_feed_id?: number | null;
          rss_item_guid?: string | null;
          ingredients?: string[] | null;
          instructions?: string[] | null;
          prep_time_minutes?: number | null;
          cook_time_minutes?: number | null;
          total_time_minutes?: number | null;
          servings?: number | null;
          difficulty_level?: string | null;
          cuisine_type?: string | null;
          scraped_data?: Record<string, unknown> | null;
          is_favorite?: boolean | null;
        };
        Update: {
          id?: number;
          title?: string;
          description?: string | null;
          image_url?: string | null;
          recipe_url?: string | null;
          user_id?: string;
          created_at?: string;
          updated_at?: string;
          rss_feed_id?: number | null;
          rss_item_guid?: string | null;
          ingredients?: string[] | null;
          instructions?: string[] | null;
          prep_time_minutes?: number | null;
          cook_time_minutes?: number | null;
          total_time_minutes?: number | null;
          servings?: number | null;
          difficulty_level?: string | null;
          cuisine_type?: string | null;
          scraped_data?: Record<string, unknown> | null;
          is_favorite?: boolean | null;
        };
      };
      rss_feeds: {
        Row: {
          id: number;
          name: string;
          url: string;
          description: string | null;
          user_id: string;
          is_active: boolean;
          last_checked_at: string | null;
          last_successful_import_at: string | null;
          import_count: number;
          error_count: number;
          last_error: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: number;
          name: string;
          url: string;
          description?: string | null;
          user_id: string;
          is_active?: boolean;
          last_checked_at?: string | null;
          last_successful_import_at?: string | null;
          import_count?: number;
          error_count?: number;
          last_error?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: number;
          name?: string;
          url?: string;
          description?: string | null;
          user_id?: string;
          is_active?: boolean;
          last_checked_at?: string | null;
          last_successful_import_at?: string | null;
          import_count?: number;
          error_count?: number;
          last_error?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      rss_import_logs: {
        Row: {
          id: number;
          rss_feed_id: number;
          status: 'success' | 'error' | 'partial';
          items_found: number;
          items_imported: number;
          items_skipped: number;
          error_message: string | null;
          started_at: string;
          completed_at: string | null;
          created_at: string;
        };
        Insert: {
          id?: number;
          rss_feed_id: number;
          status: 'success' | 'error' | 'partial';
          items_found: number;
          items_imported: number;
          items_skipped: number;
          error_message?: string | null;
          started_at: string;
          completed_at?: string | null;
          created_at?: string;
        };
        Update: {
          id?: number;
          rss_feed_id?: number;
          status?: 'success' | 'error' | 'partial';
          items_found?: number;
          items_imported?: number;
          items_skipped?: number;
          error_message?: string | null;
          started_at?: string;
          completed_at?: string | null;
          created_at?: string;
        };
      };
      folders: {
        Row: {
          id: number;
          user_id: string;
          name: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: number;
          user_id: string;
          name: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: number;
          user_id?: string;
          name?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      recipe_folders: {
        Row: {
          id: number;
          recipe_id: number;
          folder_id: number;
          created_at: string;
        };
        Insert: {
          id?: number;
          recipe_id: number;
          folder_id: number;
          created_at?: string;
        };
        Update: {
          id?: number;
          recipe_id?: number;
          folder_id?: number;
          created_at?: string;
        };
      };
    };
  };
}

export type Recipe = Database['public']['Tables']['recipes']['Row'];
export type RecipeInsert = Database['public']['Tables']['recipes']['Insert'];
export type RecipeUpdate = Database['public']['Tables']['recipes']['Update'];

// Extended Recipe type with RSS feed information
export type RecipeWithRssFeed = Recipe & {
  rss_feeds?: {
    id: number;
    name: string;
    url: string;
    description: string | null;
  } | null;
};

export type RssFeed = Database['public']['Tables']['rss_feeds']['Row'];
export type RssFeedInsert = Database['public']['Tables']['rss_feeds']['Insert'];
export type RssFeedUpdate = Database['public']['Tables']['rss_feeds']['Update'];

export type RssImportLog =
  Database['public']['Tables']['rss_import_logs']['Row'];
export type RssImportLogInsert =
  Database['public']['Tables']['rss_import_logs']['Insert'];
export type RssImportLogUpdate =
  Database['public']['Tables']['rss_import_logs']['Update'];

export type Folder = Database['public']['Tables']['folders']['Row'];
export type FolderInsert = Database['public']['Tables']['folders']['Insert'];
export type FolderUpdate = Database['public']['Tables']['folders']['Update'];

export type RecipeFolder = Database['public']['Tables']['recipe_folders']['Row'];
export type RecipeFolderInsert = Database['public']['Tables']['recipe_folders']['Insert'];
export type RecipeFolderUpdate = Database['public']['Tables']['recipe_folders']['Update'];

// Extended types with relationships
export type FolderWithRecipeCount = Folder & {
  recipe_count: number;
};

export type RecipeWithFolders = Recipe & {
  folders?: Folder[];
  rss_feeds?: {
    id: number;
    name: string;
    url: string;
    description: string | null;
  } | null;
};

// RSS Feed Test Result Type
export interface RssFeedTestResult {
  valid: boolean;
  error?: string;
  feedTitle?: string;
  sampleItems?: Array<{ title: string; url?: string }>;
  domain?: string;
}
