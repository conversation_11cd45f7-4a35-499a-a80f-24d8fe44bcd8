/**
 * Utility functions for extracting images from web pages
 */

export interface ImageExtractionResult {
  images: string[];
  error?: string;
}

/**
 * Scrape images from a given URL using the API route
 */
export async function scrapeImagesFromUrl(
  url: string,
  proxy: boolean = false
): Promise<ImageExtractionResult> {
  try {
    const response = await fetch('/api/scrape-image', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ url, proxy }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      const errorMessage = errorData.error || 'Failed to scrape images';

      return {
        images: [],
        error: errorMessage,
      };
    }

    const data = await response.json();
    const images = data.images || [];

    return {
      images,
    };
  } catch {
    return {
      images: [],
      error: 'Network error while scraping images',
    };
  }
}

/**
 * Get the best image from a list of scraped images
 * Prioritizes images that are likely to be recipe/food related
 */
export function getBestImage(images: string[]): string | null {
  if (images.length === 0) {
    return null;
  }

  // Score images based on likelihood of being a good recipe image
  const scoredImages = images.map((url) => ({
    url,
    score: scoreImageUrl(url),
  }));

  // Sort by score (highest first)
  scoredImages.sort((a, b) => b.score - a.score);

  return scoredImages[0].url;
}

/**
 * Score an image URL based on how likely it is to be a good recipe image
 */
function scoreImageUrl(url: string): number {
  let score = 0;
  const lowerUrl = url.toLowerCase();

  // Positive indicators
  if (lowerUrl.includes('recipe')) score += 10;
  if (lowerUrl.includes('food')) score += 8;
  if (lowerUrl.includes('dish')) score += 6;
  if (lowerUrl.includes('cooking')) score += 6;
  if (lowerUrl.includes('meal')) score += 5;
  if (lowerUrl.includes('kitchen')) score += 3;

  // Image quality indicators
  if (lowerUrl.includes('large') || lowerUrl.includes('big')) score += 3;
  if (lowerUrl.includes('high') || lowerUrl.includes('hd')) score += 2;
  if (lowerUrl.includes('featured')) score += 5;
  if (lowerUrl.includes('hero')) score += 4;
  if (lowerUrl.includes('main')) score += 3;

  // Negative indicators
  if (lowerUrl.includes('logo')) score -= 10;
  if (lowerUrl.includes('icon')) score -= 8;
  if (lowerUrl.includes('avatar')) score -= 8;
  if (lowerUrl.includes('profile')) score -= 6;
  if (lowerUrl.includes('thumb')) score -= 3;
  if (lowerUrl.includes('small')) score -= 2;
  if (lowerUrl.includes('mini')) score -= 3;

  // Domain-based scoring
  if (lowerUrl.includes('unsplash.com')) score += 5;
  if (lowerUrl.includes('pexels.com')) score += 5;
  if (lowerUrl.includes('pixabay.com')) score += 4;
  if (lowerUrl.includes('cdn.')) score += 2;

  return score;
}

/**
 * Find and return the best available image for a recipe URL
 */
export async function findBestImageForRecipe(
  recipeUrl: string
): Promise<string | null> {
  try {
    // Use the enhanced scrape-image API with proxy enabled
    const result = await scrapeImagesFromUrl(recipeUrl, true);

    // If scraping was successful, use those images
    if (!result.error && result.images.length > 0) {
      const bestImage = getBestImage(result.images);

      if (bestImage) {
        // Since we're using proxied URLs, we can return directly
        return bestImage;
      }

      // If no best image found, return the first one
      return result.images[0];
    }

    return null;
  } catch {
    return null;
  }
}
