import Parser from 'rss-parser';

export interface RssItem {
  title?: string;
  link?: string;
  description?: string;
  pubDate?: string;
  guid?: string;
  author?: string;
  categories?: string[];
  content?: string;
  contentSnippet?: string;
  enclosure?: {
    url?: string;
    type?: string;
    length?: string;
  };
  isoDate?: string;
}

export interface RssFeedData {
  title: string;
  description?: string;
  link: string;
  items: RssItem[];
  lastBuildDate?: string;
  language?: string;
  generator?: string;
}

export interface ParsedRecipeItem {
  title: string;
  url: string;
  description: string;
  publishedAt: Date | null;
  guid: string;
  author?: string;
  categories: string[];
  imageUrl?: string;
  content?: string;
}

/**
 * RSS Parser class for fetching and parsing RSS feeds
 */
export class RssParser {
  private parser: Parser;

  constructor() {
    this.parser = new Parser({
      timeout: 10000, // 10 second timeout
      headers: {
        'User-Agent': 'Recipe-Picker RSS Reader 1.0',
        Accept: 'application/rss+xml, application/xml, text/xml',
      },
      customFields: {
        item: [
          ['media:content', 'mediaContent'],
          ['media:thumbnail', 'mediaThumbnail'],
          ['content:encoded', 'contentEncoded'],
          ['dc:creator', 'creator'],
          ['dc:date', 'dcDate'],
        ],
      },
    });
  }

  /**
   * Fetch and parse an RSS feed
   */
  async parseFeed(url: string): Promise<RssFeedData> {
    try {
      const feed = await this.parser.parseURL(url);

      return {
        title: feed.title || 'Unknown Feed',
        description: feed.description,
        link: feed.link || url,
        items: (feed.items || []) as RssItem[],
        lastBuildDate: feed.lastBuildDate,
        language: feed.language,
        generator: feed.generator,
      };
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`Failed to parse RSS feed: ${error.message}`);
      }
      throw new Error('Failed to parse RSS feed: Unknown error');
    }
  }

  /**
   * Extract recipe data from RSS items
   */
  extractRecipeItems(items: RssItem[]): ParsedRecipeItem[] {
    return items
      .filter((item) => this.isRecipeItem(item))
      .map((item) => this.parseRecipeItem(item))
      .filter((item): item is ParsedRecipeItem => item !== null);
  }

  /**
   * Check if an RSS item appears to be a recipe
   */
  private isRecipeItem(item: RssItem): boolean {
    const title = item.title?.toLowerCase() || '';
    const description = item.description?.toLowerCase() || '';
    const categories = item.categories?.map((cat) => cat.toLowerCase()) || [];

    // Recipe-related keywords
    const recipeKeywords = [
      'recipe',
      'recipes',
      'cooking',
      'baking',
      'food',
      'dish',
      'meal',
      'ingredient',
      'ingredients',
      'cook',
      'bake',
      'kitchen',
      'culinary',
    ];

    // Check if any recipe keywords appear in title, description, or categories
    const hasRecipeKeywords = recipeKeywords.some(
      (keyword) =>
        title.includes(keyword) ||
        description.includes(keyword) ||
        categories.some((cat) => cat.includes(keyword))
    );

    // Exclude non-recipe content
    const excludeKeywords = [
      'news',
      'review',
      'restaurant',
      'event',
      'announcement',
      'giveaway',
      'contest',
      'interview',
      'travel',
      'equipment',
    ];

    const hasExcludeKeywords = excludeKeywords.some(
      (keyword) => title.includes(keyword) || description.includes(keyword)
    );

    return hasRecipeKeywords && !hasExcludeKeywords;
  }

  /**
   * Parse a single RSS item into recipe data
   */
  private parseRecipeItem(item: RssItem): ParsedRecipeItem | null {
    if (!item.title || !item.link) {
      return null;
    }

    // Extract publication date
    let publishedAt: Date | null = null;
    if (item.isoDate) {
      publishedAt = new Date(item.isoDate);
    } else if (item.pubDate) {
      publishedAt = new Date(item.pubDate);
    }

    // Extract description
    const description = this.extractDescription(item);

    // Extract image URL
    const imageUrl = this.extractImageUrl(item);

    // Extract author
    const author = this.extractAuthor(item);

    // Extract categories
    const categories = item.categories || [];

    // Generate GUID if not present
    const guid =
      item.guid ||
      item.link ||
      `${item.title}-${publishedAt?.getTime() || Date.now()}`;

    return {
      title: item.title.trim(),
      url: item.link.trim(),
      description: description.trim(),
      publishedAt,
      guid,
      author,
      categories,
      imageUrl,
      content: item.content || item.contentSnippet,
    };
  }

  /**
   * Extract description from RSS item
   */
  private extractDescription(item: RssItem): string {
    // Try different description fields
    const descriptions = [
      item.contentSnippet,
      item.description,
      item.content,
      (item as Record<string, unknown>).contentEncoded as string,
    ].filter(Boolean);

    if (descriptions.length === 0) {
      return '';
    }

    // Use the longest description (likely most complete)
    const description =
      descriptions.reduce((longest, current) =>
        (current?.length || 0) > (longest?.length || 0) ? current : longest
      ) || '';

    // Clean HTML tags and decode entities
    return this.cleanHtmlContent(description);
  }

  /**
   * Extract image URL from RSS item
   */
  private extractImageUrl(item: RssItem): string | undefined {
    // Check enclosure for images
    if (item.enclosure?.type?.startsWith('image/')) {
      return item.enclosure.url;
    }

    // Check media content
    const mediaContent = (item as Record<string, unknown>).mediaContent;
    const mediaContentTyped = mediaContent as {
      $?: { type?: string; url?: string };
    };
    if (
      mediaContentTyped?.$ &&
      mediaContentTyped.$.type?.startsWith('image/')
    ) {
      return mediaContentTyped.$.url;
    }

    // Check media thumbnail
    const mediaThumbnail = (item as Record<string, unknown>).mediaThumbnail;
    const mediaThumbnailTyped = mediaThumbnail as { $?: { url?: string } };
    if (mediaThumbnailTyped?.$?.url) {
      return mediaThumbnailTyped.$.url;
    }

    // Extract from description HTML
    const description = item.description || item.content || '';
    const imgMatch = description.match(/<img[^>]+src=["']([^"']+)["'][^>]*>/i);
    if (imgMatch) {
      return imgMatch[1];
    }

    return undefined;
  }

  /**
   * Extract author from RSS item
   */
  private extractAuthor(item: RssItem): string | undefined {
    return (
      item.author ||
      ((item as Record<string, unknown>).creator as string) ||
      undefined
    );
  }

  /**
   * Clean HTML content and decode entities
   */
  private cleanHtmlContent(html: string): string {
    if (!html) return '';

    // Remove HTML tags
    let cleaned = html.replace(/<[^>]*>/g, '');

    // Decode common HTML entities
    const entities: Record<string, string> = {
      '&amp;': '&',
      '&lt;': '<',
      '&gt;': '>',
      '&quot;': '"',
      '&#39;': "'",
      '&nbsp;': ' ',
    };

    Object.entries(entities).forEach(([entity, char]) => {
      cleaned = cleaned.replace(new RegExp(entity, 'g'), char);
    });

    // Clean up whitespace
    cleaned = cleaned.replace(/\s+/g, ' ').trim();

    // Limit length for description
    if (cleaned.length > 500) {
      cleaned = cleaned.substring(0, 497) + '...';
    }

    return cleaned;
  }

  /**
   * Validate RSS feed URL by attempting to fetch it
   */
  async validateFeedUrl(
    url: string
  ): Promise<{ isValid: boolean; error?: string; title?: string }> {
    try {
      const feed = await this.parseFeed(url);
      return {
        isValid: true,
        title: feed.title,
      };
    } catch (error) {
      return {
        isValid: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}
