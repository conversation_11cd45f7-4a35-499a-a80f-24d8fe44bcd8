import { Rss<PERSON>ars<PERSON>, ParsedRecipeItem } from './rssParser';
import { getRecipes } from './recipes';
import {
  RecipeInsert,
  RssFeed,
  RssFeedUpdate,
  RssImportLog,
  RssImportLogUpdate,
  Database,
} from './supabase';
import { SupabaseClient } from '@supabase/supabase-js';

export interface ImportResult {
  feedId: number;
  totalItems: number;
  importedCount: number;
  skippedCount: number;
  errors: string[];
  importedRecipes: RecipeInsert[];
}

export interface ImportOptions {
  maxItems?: number;
  skipDuplicates?: boolean;
  autoScrapeImages?: boolean;
}

/**
 * RSS Recipe Importer class
 */
export class RssRecipeImporter {
  private parser: RssParser;
  private supabase?: SupabaseClient<Database>;

  constructor(supabase?: SupabaseClient<Database>) {
    this.parser = new RssParser();
    this.supabase = supabase;
  }

  /**
   * Get RSS feed by ID using the provided Supabase client or fallback to client-side
   */
  private async getRssFeed(id: number): Promise<RssFeed | null> {
    if (this.supabase) {
      // Use server-side Supabase client
      const { data, error } = await this.supabase
        .from('rss_feeds')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No rows returned
          return null;
        }
        throw new Error('Failed to fetch RSS feed');
      }

      return data;
    } else {
      // Fallback to client-side function
      const { getRssFeed } = await import('./rssFeeds');
      return getRssFeed(id);
    }
  }

  /**
   * Import recipes from a single RSS feed
   */
  async importFromFeed(
    feedId: number,
    userId: string,
    options: ImportOptions = {}
  ): Promise<ImportResult> {
    const {
      maxItems = 50,
      skipDuplicates = true,
      autoScrapeImages = true,
    } = options;

    const startTime = new Date().toISOString();
    let logId: number | null = null;

    try {
      // Get RSS feed details
      const feed = await this.getRssFeed(feedId);
      if (!feed) {
        throw new Error('RSS feed not found');
      }

      if (!feed.is_active) {
        throw new Error('RSS feed is not active');
      }

      // Create import log
      const importLog = await this.createImportLog({
        rss_feed_id: feedId,
        status: 'success',
        items_found: 0,
        items_imported: 0,
        items_skipped: 0,
        error_message: null,
        started_at: startTime,
        completed_at: null,
      });
      logId = importLog.id;

      // Parse RSS feed
      const feedData = await this.parser.parseFeed(feed.url);
      const recipeItems = this.parser.extractRecipeItems(feedData.items);

      // Limit items if specified
      const itemsToProcess = recipeItems.slice(0, maxItems);

      // Update log with items found
      await this.updateImportLog(logId, {
        items_found: itemsToProcess.length,
      });

      const result: ImportResult = {
        feedId,
        totalItems: itemsToProcess.length,
        importedCount: 0,
        skippedCount: 0,
        errors: [],
        importedRecipes: [],
      };

      // Get existing recipes to check for duplicates
      let existingRecipes: { recipe_url: string; rss_item_guid: string }[] = [];
      if (skipDuplicates) {
        const recipes = await getRecipes();
        existingRecipes = recipes.map((r) => ({
          recipe_url: r.recipe_url || '',
          rss_item_guid: r.rss_item_guid || '',
        }));
      }

      // Process each recipe item
      for (const item of itemsToProcess) {
        try {
          // Check for duplicates
          if (skipDuplicates && this.isDuplicate(item, existingRecipes)) {
            result.skippedCount++;
            continue;
          }

          // Create recipe
          const recipe = await this.createRecipeFromItem(
            item,
            userId,
            feedId,
            autoScrapeImages
          );

          // If recipe is null, it means it was a duplicate and was handled silently
          if (recipe === null) {
            result.skippedCount++;
            continue;
          }

          result.importedRecipes.push(recipe);
          result.importedCount++;

          // Add to existing recipes to prevent duplicates in this batch
          existingRecipes.push({
            recipe_url: recipe.recipe_url || '',
            rss_item_guid: recipe.rss_item_guid || '',
          });
        } catch (error) {
          const errorMessage =
            error instanceof Error ? error.message : 'Unknown error';
          result.errors.push(
            `Failed to import "${item.title}" from ${item.url}: ${errorMessage}`
          );
          result.skippedCount++;
        }
      }

      // Update import log with final results
      const completedAt = new Date().toISOString();
      await this.updateImportLog(logId, {
        status: result.errors.length === 0 ? 'success' : 'partial',
        items_imported: result.importedCount,
        items_skipped: result.skippedCount,
        error_message:
          result.errors.length > 0 ? result.errors.join('; ') : null,
        completed_at: completedAt,
      });

      // Update RSS feed statistics
      await this.updateRssFeedStats(feedId, {
        lastCheckedAt: completedAt,
        lastSuccessfulImportAt:
          result.importedCount > 0 ? completedAt : undefined,
        importCount: feed.import_count + result.importedCount,
        errorCount:
          result.errors.length > 0 ? feed.error_count + 1 : feed.error_count,
        lastError: result.errors.length > 0 ? result.errors[0] : null,
      });

      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const completedAt = new Date().toISOString();

      // Update import log with error
      if (logId) {
        await this.updateImportLog(logId, {
          status: 'error',
          error_message: errorMessage,
          completed_at: completedAt,
        });
      }

      // Update RSS feed with error
      const feed = await this.getRssFeed(feedId);
      if (feed) {
        await this.updateRssFeedStats(feedId, {
          lastCheckedAt: completedAt,
          errorCount: feed.error_count + 1,
          lastError: errorMessage,
        });
      }

      throw error;
    }
  }

  /**
   * Get active RSS feeds using the provided Supabase client or fallback to client-side
   */
  private async getActiveFeedsForImport(): Promise<RssFeed[]> {
    if (this.supabase) {
      // Use server-side Supabase client
      const { data, error } = await this.supabase
        .from('rss_feeds')
        .select('*')
        .eq('is_active', true)
        .order('last_checked_at', { ascending: true, nullsFirst: true });

      if (error) {
        throw new Error('Failed to fetch active RSS feeds');
      }

      return data || [];
    } else {
      // Fallback to client-side function
      const { getActiveFeedsForImport } = await import('./rssFeeds');
      return getActiveFeedsForImport();
    }
  }

  /**
   * Update RSS feed statistics using the provided Supabase client or fallback to client-side
   */
  private async updateRssFeedStats(
    feedId: number,
    stats: {
      lastCheckedAt: string;
      lastSuccessfulImportAt?: string;
      importCount?: number;
      errorCount?: number;
      lastError?: string | null;
    }
  ): Promise<RssFeed> {
    if (this.supabase) {
      // Use server-side Supabase client
      const updates: RssFeedUpdate = {
        last_checked_at: stats.lastCheckedAt,
      };

      if (stats.lastSuccessfulImportAt) {
        updates.last_successful_import_at = stats.lastSuccessfulImportAt;
      }

      if (stats.importCount !== undefined) {
        updates.import_count = stats.importCount;
      }

      if (stats.errorCount !== undefined) {
        updates.error_count = stats.errorCount;
      }

      if (stats.lastError !== undefined) {
        updates.last_error = stats.lastError;
      }

      const { data, error } = await this.supabase
        .from('rss_feeds')
        .update(updates)
        .eq('id', feedId)
        .select()
        .single();

      if (error) {
        if (error.code === '23505') {
          // Unique constraint violation
          throw new Error('RSS feed URL already exists');
        }
        throw new Error('Failed to update RSS feed');
      }

      return data;
    } else {
      // Fallback to client-side function
      const { updateRssFeedStats } = await import('./rssFeeds');
      return updateRssFeedStats(feedId, stats);
    }
  }

  /**
   * Create an import log entry using the provided Supabase client or fallback to client-side
   */
  private async createImportLog(log: {
    rss_feed_id: number;
    status: 'success' | 'error' | 'partial';
    items_found: number;
    items_imported: number;
    items_skipped: number;
    error_message: string | null;
    started_at: string;
    completed_at: string | null;
  }): Promise<RssImportLog> {
    if (this.supabase) {
      // Use server-side Supabase client
      const { data, error } = await this.supabase
        .from('rss_import_logs')
        .insert(log)
        .select()
        .single();

      if (error) {
        throw new Error('Failed to create import log');
      }

      return data;
    } else {
      // Fallback to client-side function
      const { createImportLog } = await import('./rssFeeds');
      return createImportLog(log);
    }
  }

  /**
   * Update an import log entry using the provided Supabase client or fallback to client-side
   */
  private async updateImportLog(
    id: number,
    updates: Partial<
      Pick<
        RssImportLogUpdate,
        | 'status'
        | 'items_found'
        | 'items_imported'
        | 'items_skipped'
        | 'error_message'
        | 'completed_at'
      >
    >
  ): Promise<RssImportLog> {
    if (this.supabase) {
      // Use server-side Supabase client
      const { data, error } = await this.supabase
        .from('rss_import_logs')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw new Error('Failed to update import log');
      }

      return data;
    } else {
      // Fallback to client-side function
      const { updateImportLog } = await import('./rssFeeds');
      return updateImportLog(id, updates);
    }
  }

  /**
   * Import recipes from all active RSS feeds for a user
   */
  async importFromAllFeeds(
    userId: string,
    options: ImportOptions = {}
  ): Promise<ImportResult[]> {
    const feeds = await this.getActiveFeedsForImport();

    const results: ImportResult[] = [];

    for (const feed of feeds) {
      try {
        const result = await this.importFromFeed(feed.id, userId, options);
        results.push(result);
      } catch (error) {
        // Continue with other feeds even if one fails
        results.push({
          feedId: feed.id,
          totalItems: 0,
          importedCount: 0,
          skippedCount: 0,
          errors: [error instanceof Error ? error.message : 'Unknown error'],
          importedRecipes: [],
        });
      }
    }

    return results;
  }

  /**
   * Check if a recipe item is a duplicate
   */
  private isDuplicate(
    item: ParsedRecipeItem,
    existingRecipes: { recipe_url: string; rss_item_guid: string }[]
  ): boolean {
    return existingRecipes.some(
      (existing) =>
        existing.recipe_url === item.url || existing.rss_item_guid === item.guid
    );
  }

  /**
   * Create a recipe from a parsed RSS item
   * Returns null if the recipe is a duplicate (to be handled silently)
   */
  private async createRecipeFromItem(
    item: ParsedRecipeItem,
    userId: string,
    feedId: number,
    autoScrapeImages: boolean
  ): Promise<RecipeInsert | null> {
    let imageUrl = item.imageUrl;

    // Auto-scrape images if enabled and no image URL provided
    if (autoScrapeImages && !imageUrl && item.url) {
      try {
        imageUrl = await this.scrapeImageFromUrl(item.url);
      } catch {
        // Ignore image scraping errors, continue without image
        // Image scraping is optional, so we don't need to log this
      }
    }

    const recipe: RecipeInsert = {
      title: item.title,
      description: item.description || null,
      recipe_url: item.url,
      image_url: imageUrl || null,
      user_id: userId,
      rss_feed_id: feedId,
      rss_item_guid: item.guid,
    };

    // Use the server-side Supabase client if available, otherwise fallback to client-side
    if (this.supabase) {
      const { data, error } = await this.supabase
        .from('recipes')
        .insert(recipe)
        .select()
        .single();

      if (error) {
        // Handle duplicate constraint violations silently
        if (error.code === '23505') {
          // This is a duplicate recipe, return null to indicate it should be skipped silently
          return null;
        }

        // For other errors, include detailed error information in the thrown error
        const errorDetails = {
          code: error.code,
          message: error.message,
          details: error.details,
          hint: error.hint,
        };

        throw new Error(
          `Failed to create recipe: ${error.message} (Code: ${error.code}). Details: ${JSON.stringify(errorDetails)}`
        );
      }

      return data;
    } else {
      // Fallback to client-side function
      const { createRecipe } = await import('./recipes');
      return await createRecipe(recipe);
    }
  }

  /**
   * Scrape image from recipe URL using existing scrape-image API
   */
  private async scrapeImageFromUrl(url: string): Promise<string | undefined> {
    try {
      const response = await fetch('/api/scrape-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();

      // Return the first image if available
      if (data.images && data.images.length > 0) {
        return data.images[0];
      }

      return undefined;
    } catch {
      // Image scraping failed, return undefined
      return undefined;
    }
  }

  /**
   * Test RSS feed URL and return sample items
   */
  async testFeedUrl(
    url: string,
    maxItems = 5
  ): Promise<{
    isValid: boolean;
    error?: string;
    feedTitle?: string;
    sampleItems?: ParsedRecipeItem[];
  }> {
    try {
      const validation = await this.parser.validateFeedUrl(url);
      if (!validation.isValid) {
        return validation;
      }

      const feedData = await this.parser.parseFeed(url);
      const recipeItems = this.parser.extractRecipeItems(feedData.items);

      return {
        isValid: true,
        feedTitle: feedData.title,
        sampleItems: recipeItems.slice(0, maxItems),
      };
    } catch (error) {
      return {
        isValid: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}
