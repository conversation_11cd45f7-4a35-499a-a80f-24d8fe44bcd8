'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui';
import { RssFeed } from '@/lib/supabase';
import {
  Globe,
  Download,
  Edit,
  Trash2,
  Power,
  PowerOff,
  CheckCircle,
  XCircle,
  AlertCircle,
} from 'lucide-react';

interface RssFeedListProps {
  feeds: RssFeed[];
  onUpdate: (
    id: number,
    data: Partial<{ name: string; url: string; description: string }>
  ) => Promise<RssFeed>;
  onDelete: (id: number) => Promise<void>;
  onToggleStatus: (id: number, isActive: boolean) => Promise<RssFeed>;
  onImport: (feedId: number) => Promise<void>;
  isImporting: boolean;
}

const RssFeedList: React.FC<RssFeedListProps> = ({
  feeds,
  onUpdate,
  onDelete,
  onToggleStatus,
  onImport,
  isImporting,
}) => {
  const [editingId, setEditingId] = useState<number | null>(null);
  const [editData, setEditData] = useState<{
    name: string;
    url: string;
    description: string;
  }>({
    name: '',
    url: '',
    description: '',
  });

  const handleEdit = (feed: RssFeed) => {
    setEditingId(feed.id);
    setEditData({
      name: feed.name,
      url: feed.url,
      description: feed.description || '',
    });
  };

  const handleSaveEdit = async () => {
    if (editingId) {
      try {
        await onUpdate(editingId, editData);
        setEditingId(null);
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Failed to update feed:', error);
      }
    }
  };

  const handleCancelEdit = () => {
    setEditingId(null);
    setEditData({ name: '', url: '', description: '' });
  };

  const handleDelete = async (id: number, name: string) => {
    if (
      window.confirm(
        `Are you sure you want to delete "${name}"? This will also delete all imported recipes from this feed.`
      )
    ) {
      try {
        await onDelete(id);
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Failed to delete feed:', error);
      }
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusIcon = (feed: RssFeed) => {
    if (!feed.is_active) {
      return <PowerOff className="h-4 w-4 text-gray-400" />;
    }

    if (feed.error_count > 0 && feed.last_error) {
      return <XCircle className="h-4 w-4 text-red-500" />;
    }

    if (feed.import_count > 0) {
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    }

    return <AlertCircle className="h-4 w-4 text-yellow-500" />;
  };

  const getStatusText = (feed: RssFeed) => {
    if (!feed.is_active) return 'Inactive';
    if (feed.error_count > 0 && feed.last_error) return 'Error';
    if (feed.import_count > 0) return 'Active';
    return 'Not imported yet';
  };

  return (
    <div className="space-y-4">
      {feeds.map((feed) => (
        <div
          key={feed.id}
          className="bg-card border border-border rounded-lg p-6 shadow-sm"
        >
          {editingId === feed.id ? (
            // Edit mode
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Name</label>
                <input
                  type="text"
                  value={editData.name}
                  onChange={(e) =>
                    setEditData({ ...editData, name: e.target.value })
                  }
                  className="w-full px-3 py-2 border border-border rounded-md"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">URL</label>
                <input
                  type="url"
                  value={editData.url}
                  onChange={(e) =>
                    setEditData({ ...editData, url: e.target.value })
                  }
                  className="w-full px-3 py-2 border border-border rounded-md"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">
                  Description
                </label>
                <textarea
                  value={editData.description}
                  onChange={(e) =>
                    setEditData({ ...editData, description: e.target.value })
                  }
                  className="w-full px-3 py-2 border border-border rounded-md"
                  rows={2}
                />
              </div>
              <div className="flex gap-2">
                <Button onClick={handleSaveEdit} size="sm">
                  Save
                </Button>
                <Button onClick={handleCancelEdit} variant="outline" size="sm">
                  Cancel
                </Button>
              </div>
            </div>
          ) : (
            // View mode
            <div>
              <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4 mb-4">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-2 flex-wrap">
                    {getStatusIcon(feed)}
                    <h3 className="text-lg font-semibold truncate">{feed.name}</h3>
                    <span className="text-sm text-muted-foreground whitespace-nowrap">
                      ({getStatusText(feed)})
                    </span>
                  </div>

                  <div className="flex items-start gap-2 text-sm text-muted-foreground mb-2">
                    <Globe className="h-4 w-4 mt-0.5 flex-shrink-0" />
                    <a
                      href={feed.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="hover:text-primary underline break-all"
                    >
                      {feed.url}
                    </a>
                  </div>

                  {feed.description && (
                    <p className="text-sm text-muted-foreground mb-3">
                      {feed.description}
                    </p>
                  )}

                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-4 text-sm">
                    <div className="flex justify-between sm:block">
                      <span className="font-medium">Imported:</span>
                      <span className="ml-1">{feed.import_count} recipes</span>
                    </div>
                    <div className="flex justify-between sm:block">
                      <span className="font-medium">Last checked:</span>
                      <span className="ml-1 text-right sm:text-left">
                        {formatDate(feed.last_checked_at)}
                      </span>
                    </div>
                    <div className="flex justify-between sm:block">
                      <span className="font-medium">Last import:</span>
                      <span className="ml-1 text-right sm:text-left">
                        {formatDate(feed.last_successful_import_at)}
                      </span>
                    </div>
                    <div className="flex justify-between sm:block">
                      <span className="font-medium">Errors:</span>
                      <span className="ml-1">{feed.error_count}</span>
                    </div>
                  </div>

                  {feed.last_error && (
                    <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                      <strong>Last error:</strong> {feed.last_error}
                    </div>
                  )}
                </div>

                <div className="flex flex-col sm:flex-row gap-2 sm:ml-4 w-full sm:w-auto">
                  <Button
                    onClick={() => onImport(feed.id)}
                    disabled={isImporting || !feed.is_active}
                    size="sm"
                    variant="outline"
                    className="flex items-center justify-center gap-1 w-full sm:w-auto"
                  >
                    <Download className="h-4 w-4" />
                    <span className="sm:inline">Import</span>
                  </Button>

                  <Button
                    onClick={() => onToggleStatus(feed.id, !feed.is_active)}
                    size="sm"
                    variant="outline"
                    className="flex items-center justify-center gap-1 w-full sm:w-auto"
                  >
                    {feed.is_active ? (
                      <>
                        <PowerOff className="h-4 w-4" />
                        <span className="sm:inline">Disable</span>
                      </>
                    ) : (
                      <>
                        <Power className="h-4 w-4" />
                        <span className="sm:inline">Enable</span>
                      </>
                    )}
                  </Button>

                  <Button
                    onClick={() => handleEdit(feed)}
                    size="sm"
                    variant="outline"
                    className="flex items-center justify-center gap-1 w-full sm:w-auto"
                  >
                    <Edit className="h-4 w-4" />
                    <span className="sm:inline">Edit</span>
                  </Button>

                  <Button
                    onClick={() => handleDelete(feed.id, feed.name)}
                    size="sm"
                    variant="outline"
                    className="flex items-center justify-center gap-1 text-red-600 hover:text-red-700 w-full sm:w-auto"
                  >
                    <Trash2 className="h-4 w-4" />
                    <span className="sm:inline">Delete</span>
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default RssFeedList;
