'use client';

import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  Button,
  Textarea,
} from '@/components/ui';
import { Download, Loader2, CheckCircle, XCircle } from 'lucide-react';

interface ImportResult {
  url: string;
  success: boolean;
  recipe?: unknown;
  error?: string;
}

interface BulkImportResponse {
  success: boolean;
  imported: number;
  failed: number;
  results: ImportResult[];
}

interface ImportRecipesModalProps {
  isOpen: boolean;
  onClose: () => void;
  onImportComplete?: (results: BulkImportResponse) => void;
  onRefresh?: () => void;
}

const ImportRecipesModal: React.FC<ImportRecipesModalProps> = ({
  isOpen,
  onClose,
  onImportComplete,
  onRefresh,
}) => {
  const [urls, setUrls] = useState('');
  const [isImporting, setIsImporting] = useState(false);
  const [importResults, setImportResults] = useState<BulkImportResponse | null>(
    null
  );
  const [autoScrape, setAutoScrape] = useState(true);

  const handleImport = async () => {
    if (!urls.trim()) return;

    setIsImporting(true);
    setImportResults(null);

    try {
      // Parse URLs from textarea (split by newlines and filter empty lines)
      const urlList = urls
        .split('\n')
        .map((url) => url.trim())
        .filter((url) => url.length > 0);

      if (urlList.length === 0) {
        throw new Error('No valid URLs provided');
      }

      const response = await fetch('/api/recipes/bulk-import', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          urls: urlList,
          autoScrape,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to import recipes');
      }

      const results: BulkImportResponse = await response.json();
      setImportResults(results);
      onImportComplete?.(results);
    } catch (error) {
      setImportResults({
        success: false,
        imported: 0,
        failed: 1,
        results: [
          {
            url: 'Error',
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
          },
        ],
      });
    } finally {
      setIsImporting(false);
    }
  };

  const handleClose = () => {
    if (!isImporting) {
      setUrls('');
      setImportResults(null);
      onClose();
    }
    // New results to import, refresh
    if (onRefresh && importResults) {
      onRefresh();
    }
  };

  const handleOpenChange = (open: boolean) => {
    if (!open && !isImporting) {
      handleClose();
    }
  };

  const getResultIcon = (result: ImportResult) => {
    if (!result.success) {
      return <XCircle className="h-4 w-4 text-red-500" />;
    }
    return <CheckCircle className="h-4 w-4 text-green-500" />;
  };

  const urlCount = urls
    .split('\n')
    .filter((url) => url.trim().length > 0).length;

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[85vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Import Multiple Recipes
          </DialogTitle>
          <DialogDescription>
            Enter recipe URLs (one per line) to import multiple recipes at once.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {!importResults && (
            <>
              <div className="space-y-2">
                <label htmlFor="recipe-urls" className="text-sm font-medium">
                  Recipe URLs
                </label>
                <Textarea
                  id="recipe-urls"
                  placeholder={`https://example.com/recipe1
https://example.com/recipe2
https://example.com/recipe3`}
                  value={urls}
                  onChange={(e) => setUrls(e.target.value)}
                  className="min-h-[120px] font-mono text-sm"
                  disabled={isImporting}
                />
                {urlCount > 0 && (
                  <p className="text-sm text-muted-foreground">
                    {urlCount} URL{urlCount !== 1 ? 's' : ''} detected
                  </p>
                )}
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="auto-scrape"
                  checked={autoScrape}
                  onChange={(e) => setAutoScrape(e.target.checked)}
                  disabled={isImporting}
                  className="rounded border-gray-300"
                />
                <label htmlFor="auto-scrape" className="text-sm">
                  Automatically scrape recipe details (title, description,
                  ingredients, etc.)
                </label>
              </div>

              {isImporting && (
                <div className="flex items-center justify-center py-8">
                  <div className="text-center">
                    <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
                    <p className="text-lg font-medium">Importing recipes...</p>
                    <p className="text-sm text-muted-foreground">
                      This may take a few moments while we fetch and process the
                      recipes.
                    </p>
                  </div>
                </div>
              )}
            </>
          )}

          {importResults && (
            <div className="space-y-6">
              {/* Summary */}
              <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-6">
                <h3 className="font-semibold text-lg mb-4 text-green-800">
                  Import Summary
                </h3>
                <div className="grid grid-cols-2 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600 mb-1">
                      {importResults.imported}
                    </div>
                    <div className="text-sm font-medium text-green-700">
                      Successfully Imported
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600 mb-1">
                      {importResults.failed}
                    </div>
                    <div className="text-sm font-medium text-red-700">
                      Failed
                    </div>
                  </div>
                </div>
              </div>

              {/* Detailed Results */}
              <div>
                <h3 className="font-semibold text-lg mb-4">Import Results</h3>
                <div className="space-y-3 max-h-[350px] overflow-y-auto">
                  {importResults.results.map((result, index) => (
                    <div
                      key={index}
                      className={`border rounded-lg p-4 ${
                        result.success
                          ? 'bg-green-50 border-green-200'
                          : 'bg-red-50 border-red-200'
                      }`}
                    >
                      <div className="flex items-start gap-3">
                        {getResultIcon(result)}
                        <div className="flex-1 min-w-0">
                          <div className="text-sm font-medium break-all mb-1">
                            {result.url}
                          </div>
                          {result.error && (
                            <div className="text-xs text-red-700 mt-2 p-2 bg-red-100 rounded border border-red-200">
                              {result.error}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Success Message */}
              {importResults.imported > 0 && (
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 border-2 border-green-300 rounded-xl p-6 shadow-sm">
                  <div className="flex items-center gap-3 text-green-800 mb-3">
                    <div className="bg-green-100 p-2 rounded-full">
                      <CheckCircle className="h-6 w-6 text-green-600" />
                    </div>
                    <span className="font-bold text-lg">
                      Import Completed Successfully!
                    </span>
                  </div>
                  <p className="text-green-700 text-base leading-relaxed">
                    <span className="font-semibold">
                      {importResults.imported}
                    </span>{' '}
                    new recipe
                    {importResults.imported !== 1 ? 's' : ''} have been added to
                    your collection. You can find them on your main recipes
                    page.
                  </p>
                </div>
              )}
            </div>
          )}

          <div className="flex justify-end gap-3 pt-6 border-t border-border">
            {!importResults && (
              <>
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleClose}
                  disabled={isImporting}
                  className="px-6"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleImport}
                  disabled={isImporting || urlCount === 0}
                  className="px-6"
                >
                  {isImporting ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Importing...
                    </>
                  ) : (
                    <>
                      <Download className="h-4 w-4 mr-2" />
                      Import {urlCount} Recipe{urlCount !== 1 ? 's' : ''}
                    </>
                  )}
                </Button>
              </>
            )}
            {importResults && (
              <Button
                onClick={() => {
                  handleClose();
                }}
                className="px-8"
              >
                Close
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ImportRecipesModal;
