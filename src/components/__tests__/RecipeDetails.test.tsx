import React from 'react';
import '@testing-library/jest-dom';
import { render, screen, waitFor } from '@testing-library/react';
import RecipeDetails from '../RecipeDetails';
import { Recipe } from '@/lib/supabase';
import * as recipeScraper from '@/lib/recipeScraper';

// Mock the recipe scraper
jest.mock('@/lib/recipeScraper', () => ({
  scrapeRecipeFromUrl: jest.fn(),
  convertScrapedRecipeToDbFormat: jest.fn(),
  formatTime: jest.fn((minutes) => minutes ? `${minutes} min` : ''),
  formatIngredient: jest.fn((ingredient) => ingredient),
  formatInstruction: jest.fn((instruction) => instruction),
}));

// Mock the recipes lib
jest.mock('@/lib/recipes', () => ({
  updateRecipe: jest.fn(),
}));

describe('RecipeDetails', () => {
  const mockScrapeRecipeFromUrl = jest.spyOn(recipeScraper, 'scrapeRecipeFromUrl');

  beforeEach(() => {
    mockScrapeRecipeFromUrl.mockResolvedValue({ error: 'Mocked error' });
  });
  const mockRecipe: Recipe = {
    id: 1,
    title: 'Test Recipe',
    description: 'A test recipe',
    image_url: null,
    recipe_url: 'https://example.com/recipe',
    user_id: 'test-user',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    rss_feed_id: null,
    rss_item_guid: null,
    ingredients: ['1 cup flour', '2 eggs'],
    instructions: ['Mix ingredients', 'Bake for 30 minutes'],
    prep_time_minutes: 15,
    cook_time_minutes: 30,
    total_time_minutes: 45,
    servings: 4,
    difficulty_level: 'easy',
    cuisine_type: 'american',
    scraped_data: null,
  };

  it('renders recipe details when data is available', () => {
    render(<RecipeDetails recipe={mockRecipe} />);
    
    expect(screen.getByText('Recipe Details')).toBeInTheDocument();
    expect(screen.getByText('Ingredients')).toBeInTheDocument();
    expect(screen.getByText('Instructions')).toBeInTheDocument();
    expect(screen.getByText('1 cup flour')).toBeInTheDocument();
    expect(screen.getByText('2 eggs')).toBeInTheDocument();
    expect(screen.getByText('Mix ingredients')).toBeInTheDocument();
    expect(screen.getByText('Bake for 30 minutes')).toBeInTheDocument();
  });

  it('shows try again button when scraping fails', async () => {
    const recipeWithoutData: Recipe = {
      ...mockRecipe,
      ingredients: null,
      instructions: null,
      prep_time_minutes: null,
      cook_time_minutes: null,
      servings: null,
    };

    render(<RecipeDetails recipe={recipeWithoutData} />);

    // Wait for the component to finish loading and show the error state with try again button
    await waitFor(() => {
      expect(screen.getByText('Try Again')).toBeInTheDocument();
    });
  });

  it('shows load button when recipe has some data but user wants to load more', () => {
    const recipeWithPartialData: Recipe = {
      ...mockRecipe,
      ingredients: ['existing ingredient'], // Has some data
      instructions: null, // Missing instructions
      prep_time_minutes: null,
      cook_time_minutes: null,
      servings: null,
    };

    render(<RecipeDetails recipe={recipeWithPartialData} />);

    // Should show the existing data and not attempt auto-scraping
    expect(screen.getByText('Recipe Details')).toBeInTheDocument();
    expect(screen.getByText('existing ingredient')).toBeInTheDocument();
    // Should not show the load button since it has some data
    expect(screen.queryByText('Load Recipe Details')).not.toBeInTheDocument();
  });

  it('does not render when no recipe URL and no data', () => {
    const recipeWithoutUrlOrData: Recipe = {
      ...mockRecipe,
      recipe_url: null,
      ingredients: null,
      instructions: null,
      prep_time_minutes: null,
      cook_time_minutes: null,
      servings: null,
    };

    const { container } = render(<RecipeDetails recipe={recipeWithoutUrlOrData} />);
    
    expect(container.firstChild).toBeNull();
  });

  it('displays recipe meta information correctly', () => {
    render(<RecipeDetails recipe={mockRecipe} />);
    
    expect(screen.getByText('Prep Time')).toBeInTheDocument();
    expect(screen.getByText('Cook Time')).toBeInTheDocument();
    expect(screen.getByText('Servings')).toBeInTheDocument();
    expect(screen.getByText('Difficulty')).toBeInTheDocument();
    expect(screen.getByText('4')).toBeInTheDocument(); // servings
    expect(screen.getByText('easy')).toBeInTheDocument(); // difficulty
  });
});
