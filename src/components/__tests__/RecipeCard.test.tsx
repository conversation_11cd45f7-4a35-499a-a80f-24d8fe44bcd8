import React from 'react';
import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';
import RecipeCard from '../Card';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
  }),
}));

describe('RecipeCard', () => {
  it('renders recipe card with delete button and tags', () => {
    render(
      <RecipeCard
        recipe={{
          id: 1,
          title: 'Test Recipe',
          description: 'A test description',
          image_url: null,
          recipe_url: 'https://example.com/recipe',
          user_id: 'test-user-id',
          created_at: '',
          updated_at: '',
          rss_feed_id: null,
          rss_item_guid: null,
          ingredients: ['chicken', 'rice', 'vegetables'],
          instructions: null,
          prep_time_minutes: null,
          cook_time_minutes: null,
          total_time_minutes: null,
          servings: null,
          difficulty_level: null,
          cuisine_type: 'Asian',
          scraped_data: null,
        }}
      />
    );

    // Check that the recipe title is displayed
    expect(screen.getByText('Test Recipe')).toBeInTheDocument();

    // Check that the description is displayed
    expect(screen.getByText('A test description')).toBeInTheDocument();

    // Check that the delete button is present
    const deleteButton = screen.getByRole('button', { name: /delete/i });
    expect(deleteButton).toBeInTheDocument();

    // Check that the recipe button is present (since recipe_url is provided)
    const recipeButton = screen.getByRole('button', { name: /recipe/i });
    expect(recipeButton).toBeInTheDocument();

    // Check that cuisine tag is displayed
    expect(screen.getByText('Asian')).toBeInTheDocument();

    // Check that ingredient tags are displayed
    expect(screen.getByText('Chicken')).toBeInTheDocument();
  });
});
