'use client';

import React, { useMemo, useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  Button,
} from '@/components/ui';
import { updateRecipe, deleteRecipe } from '@/lib/recipes';
import { RecipeWithRssFeed } from '@/lib/supabase';
import { useRecipeForm, RecipeFormData } from '@/hooks/useRecipeForm';
import RecipeForm from '@/components/RecipeForm';
import DeleteConfirmationModal from '@/components/DeleteConfirmationModal';

interface EditRecipeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onRecipeUpdated?: (updatedRecipe: RecipeWithRssFeed) => void;
  onRecipeDeleted?: (deletedRecipeId: number) => void;
  recipe: RecipeWithRssFeed;
}

const EditRecipeModal: React.FC<EditRecipeModalProps> = ({
  isOpen,
  onClose,
  onRecipeUpdated,
  onRecipeDeleted,
  recipe,
}) => {
  // State for delete functionality
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  // Memoize recipe data to prevent unnecessary re-renders
  const recipeData = useMemo(
    () => ({
      title: recipe.title || '',
      description: recipe.description || '',
      image_url: recipe.image_url || '',
      recipe_url: recipe.recipe_url || '',
      cuisine_type: recipe.cuisine_type || '',
    }),
    [
      recipe.title,
      recipe.description,
      recipe.image_url,
      recipe.recipe_url,
      recipe.cuisine_type,
    ]
  );

  const handleSubmit = async (data: RecipeFormData) => {
    const updatedRecipe = await updateRecipe(recipe.id, {
      title: data.title,
      description: data.description || null,
      image_url: data.image_url || null,
      recipe_url: data.recipe_url || null,
      cuisine_type: data.cuisine_type || null,
    });

    // Notify parent component
    onRecipeUpdated?.(updatedRecipe);
    onClose();
  };

  const handleCancel = () => {
    resetForm(recipeData);
    onClose();
  };

  // Delete handlers
  const handleDeleteClick = () => {
    setIsDeleteModalOpen(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      setIsDeleting(true);
      await deleteRecipe(recipe.id);
      // Notify parent component
      onRecipeDeleted?.(recipe.id);
      onClose();
    } catch {
      // You might want to show a toast notification here
    } finally {
      setIsDeleting(false);
    }
  };

  const handleDeleteCancel = () => {
    setIsDeleteModalOpen(false);
  };

  const {
    formData,
    errors,
    isLoading,
    handleInputChange,
    handleSubmit: onFormSubmit,
    resetForm,
  } = useRecipeForm({
    initialData: recipeData,
    onSubmit: handleSubmit,
    onCancel: handleCancel,
  });

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      onClose();
    }
  };

  // Handle click events on the dialog overlay to prevent default and stop propagation
  const handleOverlayClick = (event: Event) => {
    event.preventDefault();
    event.stopPropagation();
    onClose();
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={handleOpenChange}>
        <DialogContent
          className="sm:max-w-[425px]"
          onPointerDownOutside={handleOverlayClick}
          onEscapeKeyDown={(event) => {
            event.preventDefault();
            event.stopPropagation();
            onClose();
          }}
        >
          <DialogHeader>
            <DialogTitle>Edit Recipe</DialogTitle>
            <DialogDescription>
              Update the details of your recipe below.
            </DialogDescription>
          </DialogHeader>
          <RecipeForm
            formData={formData}
            errors={errors}
            isLoading={isLoading}
            onInputChange={handleInputChange}
            onSubmit={onFormSubmit}
            onCancel={handleCancel}
            submitButtonText="Update Recipe"
            loadingButtonText="Updating..."
            idPrefix="edit"
          />

          {/* Delete button section */}
          <div className="border-t pt-4 mt-4">
            <div className="flex justify-start">
              <Button
                type="button"
                variant="outline"
                onClick={handleDeleteClick}
                disabled={isLoading || isDeleting}
                className="text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200 hover:border-red-300"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 mr-2"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                  />
                </svg>
                Delete Recipe
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title={recipe.title || 'Untitled Recipe'}
        isLoading={isDeleting}
      />
    </>
  );
};

export default EditRecipeModal;
