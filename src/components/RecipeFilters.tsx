'use client';

import React, { useState } from 'react';
import { Button, Input } from '@/components/ui';
import { Tag } from '@/components/ui';
import { Recipe } from '@/lib/supabase';
import {
  getIngredientsWithCounts,
  getCuisineTypesWithCounts,
} from '@/lib/tagUtils';

export interface RecipeFiltersProps {
  recipes: Recipe[];
  searchQuery: string;
  selectedCuisines: string[];
  selectedIngredients: string[];
  onSearchChange: (query: string) => void;
  onCuisineToggle: (cuisine: string) => void;
  onIngredientToggle: (ingredient: string) => void;
  onClearFilters: () => void;
}

const RecipeFilters: React.FC<RecipeFiltersProps> = ({
  recipes,
  searchQuery,
  selectedCuisines,
  selectedIngredients,
  onSearchChange,
  onCuisineToggle,
  onIngredientToggle,
  onClearFilters,
}) => {
  const [showFilters, setShowFilters] = useState(false);
  const [showAllCuisines, setShowAllCuisines] = useState(false);
  const [showAllIngredients, setShowAllIngredients] = useState(false);

  const cuisinesWithCounts = getCuisineTypesWithCounts(recipes);
  const ingredientsWithCounts = getIngredientsWithCounts(recipes);

  const hasActiveFilters =
    selectedCuisines.length > 0 || selectedIngredients.length > 0;

  const displayedCuisines = showAllCuisines
    ? cuisinesWithCounts
    : cuisinesWithCounts.slice(0, 6);
  const displayedIngredients = showAllIngredients
    ? ingredientsWithCounts
    : ingredientsWithCounts.slice(0, 8);

  return (
    <div className="space-y-4">
      {/* Search Bar */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg
              className="h-4 w-4 text-foreground/40"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>
          <Input
            type="text"
            placeholder="Search recipes..."
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
            className={hasActiveFilters ? 'bg-blue-50 border-blue-200' : ''}
          >
            <svg
              className="h-4 w-4 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z"
              />
            </svg>
            Filters
            {hasActiveFilters && (
              <span className="ml-1 bg-blue-600 text-white text-xs rounded-full px-1.5 py-0.5">
                {selectedCuisines.length + selectedIngredients.length}
              </span>
            )}
          </Button>

          {hasActiveFilters && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearFilters}
              className="text-gray-500 hover:text-gray-700"
            >
              Clear
            </Button>
          )}
        </div>
      </div>

      {/* Filter Panel */}
      {showFilters && (
        <div className="bg-gray-50 rounded-lg p-4 space-y-4">
          {/* Cuisine Filters */}
          {cuisinesWithCounts.length > 0 && (
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">
                Cuisine Type
              </h3>
              <div className="flex flex-wrap gap-2">
                {displayedCuisines.map((cuisine) => (
                  <Tag
                    key={cuisine.name}
                    variant={
                      selectedCuisines.includes(cuisine.name)
                        ? 'cuisine'
                        : 'default'
                    }
                    onClick={() => onCuisineToggle(cuisine.name)}
                    className={`cursor-pointer ${
                      selectedCuisines.includes(cuisine.name)
                        ? 'ring-2 ring-blue-300'
                        : 'hover:bg-gray-200'
                    }`}
                  >
                    {cuisine.name} ({cuisine.count})
                  </Tag>
                ))}
                {cuisinesWithCounts.length > 6 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowAllCuisines(!showAllCuisines)}
                    className="text-xs h-6 px-2"
                  >
                    {showAllCuisines
                      ? 'Show less'
                      : `+${cuisinesWithCounts.length - 6} more`}
                  </Button>
                )}
              </div>
            </div>
          )}

          {/* Ingredient Filters */}
          {ingredientsWithCounts.length > 0 && (
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">
                Ingredients
              </h3>
              <div className="flex flex-wrap gap-2">
                {displayedIngredients.map((ingredient) => (
                  <Tag
                    key={ingredient.name}
                    variant={
                      selectedIngredients.includes(ingredient.name)
                        ? 'ingredient'
                        : 'default'
                    }
                    onClick={() => onIngredientToggle(ingredient.name)}
                    className={`cursor-pointer ${
                      selectedIngredients.includes(ingredient.name)
                        ? 'ring-2 ring-green-300'
                        : 'hover:bg-gray-200'
                    }`}
                  >
                    {ingredient.name} ({ingredient.count})
                  </Tag>
                ))}
                {ingredientsWithCounts.length > 8 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowAllIngredients(!showAllIngredients)}
                    className="text-xs h-6 px-2"
                  >
                    {showAllIngredients
                      ? 'Show less'
                      : `+${ingredientsWithCounts.length - 8} more`}
                  </Button>
                )}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default RecipeFilters;
