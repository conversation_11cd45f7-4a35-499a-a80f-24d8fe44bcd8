'use client';

import React from 'react';

interface DarkModeToggleProps {
  darkMode: boolean;
  setDarkMode: (darkMode: boolean) => void;
}

const DarkModeToggle: React.FC<DarkModeToggleProps> = ({
  darkMode,
  setDarkMode,
}) => {
  return (
    <div className="bg-background dark:bg-secondary px-3 py-2 rounded-full shadow-md">
      <label className="flex items-center cursor-pointer">
        <div className="relative">
          <input
            type="checkbox"
            className="sr-only"
            checked={darkMode}
            onChange={() => setDarkMode(!darkMode)}
          />
          <div className="w-10 h-4 bg-secondary dark:bg-background rounded-full shadow-inner"></div>
          <div
            className={`absolute left-0 top-[-3px] w-6 h-6 bg-primary rounded-full shadow transition-transform duration-300 ease-in-out ${darkMode ? 'transform translate-x-full' : ''}`}
          >
            <span className="absolute inset-0 flex items-center justify-center text-xs">
              {darkMode ? '🌙' : '☀️'}
            </span>
          </div>
        </div>
        <span className="ml-3 text-sm font-medium text-foreground">
          {darkMode ? 'Dark' : 'Light'}
        </span>
      </label>
    </div>
  );
};

export default DarkModeToggle;
