'use client';

import React, { useState, useCallback, memo } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
  Popover,
  PopoverTrigger,
  PopoverContent,
} from '@/components/ui';
import { RecipeWithRssFeed } from '@/lib/supabase';
import DeleteConfirmationModal from './DeleteConfirmationModal';
import EditRecipeModal from './EditRecipeModal';
import { deleteRecipe, toggleRecipeFavorite } from '@/lib/recipes';
import RecipeImage from './RecipeImage';
import { Tag, TagList } from '@/components/ui';
import { extractKeyIngredients } from '@/lib/tagUtils';
import Link from 'next/link';
import RecipeDetailsPreview from './RecipeDetailsPreview';
import RssFeedBadge from './RssFeedBadge';
import { useIsTouchDevice } from '@/hooks/useIsTouchDevice';
import { decodeHtmlEntities } from '@/lib/utils';
import { Star, Edit, Trash2, ExternalLink, Clock, Users } from 'lucide-react';

interface CardProps {
  recipe: RecipeWithRssFeed;
  onDelete?: (id: number) => void;
  onUpdate?: (updatedRecipe: RecipeWithRssFeed) => void;
}

const RecipeCard: React.FC<CardProps> = ({ recipe, onDelete, onUpdate }) => {
  const { id, title, description, image_url, recipe_url, is_favorite } = recipe;
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const [isFavoriteLoading, setIsFavoriteLoading] = useState(false);
  const isTouchDevice = useIsTouchDevice();

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    setIsDeleteModalOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!onDelete) return;

    try {
      setIsDeleting(true);
      await deleteRecipe(id);
      onDelete(id);
      setIsDeleteModalOpen(false);
    } catch {
      // You might want to show a toast notification here
    } finally {
      setIsDeleting(false);
    }
  };

  const handleDeleteCancel = () => {
    setIsDeleteModalOpen(false);
    // Add a small delay to ensure modal is fully closed before allowing card clicks
    setIsEditModalOpen(false);
  };

  const handleEditClick = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    e.nativeEvent.stopImmediatePropagation();
    setIsEditModalOpen(true);
  }, []);

  const handleEditClose = useCallback(() => {
    setIsEditModalOpen(false);
  }, []);

  const handleRecipeUpdated = (updatedRecipe: RecipeWithRssFeed) => {
    onUpdate?.(updatedRecipe);
    setIsEditModalOpen(false);
  };

  const handleFavoriteToggle = useCallback(async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (isFavoriteLoading) return;

    try {
      setIsFavoriteLoading(true);
      const updatedRecipe = await toggleRecipeFavorite(id);

      // Update the recipe in the parent component
      if (onUpdate) {
        onUpdate({ ...recipe, is_favorite: updatedRecipe.is_favorite });
      }
    } catch (error) {
      // Enhanced error handling
      console.error('Failed to toggle favorite:', error);
      // TODO: Add toast notification for better UX
    } finally {
      setIsFavoriteLoading(false);
    }
  }, [id, isFavoriteLoading, onUpdate, recipe]);

  const handleMouseEnter = () => {
    // Only show popover if no modals are open and not on touch device
    if (!isEditModalOpen && !isDeleteModalOpen && !isTouchDevice) {
      setIsPopoverOpen(true);
    }
  };

  const handleMouseLeave = () => {
    if (!isTouchDevice) {
      setIsPopoverOpen(false);
    }
  };

  return (
    <>
      <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
        <PopoverTrigger asChild>
          <Link href={`/card/${id}`}>
            <Card
              className="h-full transition-all duration-300 hover:shadow-lg hover:scale-[1.02] z-10 cursor-pointer flex flex-col group border-2 border-transparent hover:border-primary/20 bg-gradient-to-br from-card to-card/95"
              onMouseEnter={handleMouseEnter}
              onMouseLeave={handleMouseLeave}
              role="article"
              aria-label={`Recipe: ${decodeHtmlEntities(title)}`}
            >
              <CardHeader className="relative p-3 sm:p-6">
                <div className="absolute top-2 right-2 sm:top-4 sm:right-4 flex gap-1 z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleFavoriteToggle}
                    disabled={isFavoriteLoading}
                    className={`h-8 w-8 p-0 rounded-full backdrop-blur-sm ${
                      is_favorite
                        ? 'text-yellow-500 hover:text-yellow-600 bg-yellow-50/80'
                        : 'text-gray-400 hover:text-yellow-500 bg-white/80'
                    } hover:bg-yellow-50/90 transition-all duration-200 shadow-sm`}
                    aria-label={is_favorite ? 'Remove from favorites' : 'Add to favorites'}
                    title={is_favorite ? 'Remove from favorites' : 'Add to favorites'}
                  >
                    <Star
                      className="h-4 w-4"
                      fill={is_favorite ? 'currentColor' : 'none'}
                    />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleEditClick}
                    className="h-8 w-8 p-0 rounded-full backdrop-blur-sm text-blue-600 hover:text-blue-700 bg-white/80 hover:bg-blue-50/90 transition-all duration-200 shadow-sm"
                    aria-label="Edit recipe"
                    title="Edit recipe"
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                </div>
                <CardTitle className="text-lg sm:text-xl font-semibold text-primary line-clamp-1 pr-16 sm:pr-20">
                  {decodeHtmlEntities(title)}
                </CardTitle>
                <CardDescription className="text-sm text-foreground/80 line-clamp-2">
                  {description ? decodeHtmlEntities(description) : ''}
                </CardDescription>
                {/* Tags Section */}
                <div className="mt-3 space-y-2">
                  <div className="flex flex-wrap gap-2">
                    {recipe.cuisine_type && (
                      <Tag variant="cuisine" size="sm">
                        {recipe.cuisine_type}
                      </Tag>
                    )}
                    {recipe.rss_feeds && (
                      <RssFeedBadge
                        feedName={recipe.rss_feeds.name}
                        feedUrl={recipe.rss_feeds.url}
                        size="sm"
                      />
                    )}
                  </div>
                  {recipe.ingredients && (
                    <TagList
                      tags={extractKeyIngredients(recipe.ingredients, 3)}
                      variant="ingredient"
                      size="sm"
                      maxTags={3}
                    />
                  )}
                </div>
              </CardHeader>
              <CardContent className="flex-grow">
                <div className="mb-4">
                  <RecipeImage
                    imageUrl={image_url}
                    recipeUrl={recipe_url}
                    title={title}
                    className="relative w-full h-48"
                    recipeId={id}
                  />
                </div>
              </CardContent>
              <CardFooter className="flex justify-between mt-auto">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDeleteClick}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                    />
                  </svg>
                  Delete
                </Button>
                {recipe_url && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      window.open(recipe_url, '_blank', 'noopener,noreferrer');
                    }}
                    className="flex items-center gap-1 px-3 py-2"
                  >
                    Recipe
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 flex-shrink-0"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M18 13v6a2 2 0 01-2 2H5a2 2 0 01-2-2V8a2 2 0 012-2h6m4-3h6m0 0v6m0-6L10 14"
                      />
                    </svg>
                  </Button>
                )}
              </CardFooter>
            </Card>
          </Link>
        </PopoverTrigger>
        {!isTouchDevice && (
          <PopoverContent
            className="w-72 sm:w-80 border-4 border-primary rounded-lg p-3 sm:p-4"
            side="right"
            align="start"
            onMouseEnter={() => setIsPopoverOpen(true)}
            onMouseLeave={handleMouseLeave}
          >
            <RecipeDetailsPreview recipe={recipe} />
          </PopoverContent>
        )}
      </Popover>

      {/* Modals rendered outside the Link component to prevent navigation issues */}
      <DeleteConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title={decodeHtmlEntities(title)}
        isLoading={isDeleting}
      />
      <EditRecipeModal
        isOpen={isEditModalOpen}
        onClose={handleEditClose}
        onRecipeUpdated={handleRecipeUpdated}
        recipe={recipe}
      />
    </>
  );
};

export default RecipeCard;
