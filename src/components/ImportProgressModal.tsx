'use client';

import React from 'react';
import { Button } from '@/components/ui';
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  CheckCircle,
  XCircle,
  AlertCircle,
  Loader2,
  Download,
} from 'lucide-react';

interface ImportResult {
  feedId: number;
  totalItems: number;
  importedCount: number;
  skippedCount: number;
  errors: string[];
}

interface ImportSummary {
  totalFeeds: number;
  totalItems: number;
  totalImported: number;
  totalSkipped: number;
  totalErrors: number;
  results: ImportResult[];
}

interface ImportProgressModalProps {
  isOpen: boolean;
  onClose: () => void;
  results: ImportSummary | null;
  isImporting: boolean;
}

const ImportProgressModal: React.FC<ImportProgressModalProps> = ({
  isOpen,
  onClose,
  results,
  isImporting,
}) => {
  const getResultIcon = (result: ImportResult) => {
    if (result.errors.length > 0) {
      return <XCircle className="h-5 w-5 text-red-500" />;
    }
    if (result.importedCount > 0) {
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    }
    return <AlertCircle className="h-5 w-5 text-yellow-500" />;
  };

  const getResultStatus = (result: ImportResult) => {
    if (result.errors.length > 0) return 'Error';
    if (result.importedCount > 0) return 'Success';
    return 'No new recipes';
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Import Progress
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {isImporting && !results && (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
                <p className="text-lg font-medium">Importing recipes...</p>
                <p className="text-sm text-muted-foreground">
                  This may take a few moments while we fetch and process the RSS
                  feeds.
                </p>
              </div>
            </div>
          )}

          {results && (
            <div className="space-y-6">
              {/* Summary */}
              <div className="bg-card border border-border rounded-lg p-4">
                <h3 className="font-semibold mb-3">Import Summary</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-primary">
                      {results.totalFeeds}
                    </div>
                    <div className="text-muted-foreground">Feeds Processed</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {results.totalImported}
                    </div>
                    <div className="text-muted-foreground">
                      Recipes Imported
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-600">
                      {results.totalSkipped}
                    </div>
                    <div className="text-muted-foreground">Recipes Skipped</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">
                      {results.totalErrors}
                    </div>
                    <div className="text-muted-foreground">Errors</div>
                  </div>
                </div>
              </div>

              {/* Detailed Results */}
              <div>
                <h3 className="font-semibold mb-3">Feed Results</h3>
                <div className="space-y-3">
                  {results.results.map((result) => (
                    <div
                      key={result.feedId}
                      className="bg-card border border-border rounded-lg p-4"
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          {getResultIcon(result)}
                          <span className="font-medium">
                            Feed {result.feedId}
                          </span>
                          <span className="text-sm text-muted-foreground">
                            ({getResultStatus(result)})
                          </span>
                        </div>
                      </div>

                      <div className="grid grid-cols-3 gap-4 text-sm mb-2">
                        <div>
                          <span className="font-medium">Found:</span>
                          <span className="ml-1">
                            {result.totalItems} items
                          </span>
                        </div>
                        <div>
                          <span className="font-medium">Imported:</span>
                          <span className="ml-1 text-green-600">
                            {result.importedCount}
                          </span>
                        </div>
                        <div>
                          <span className="font-medium">Skipped:</span>
                          <span className="ml-1 text-yellow-600">
                            {result.skippedCount}
                          </span>
                        </div>
                      </div>

                      {result.errors.length > 0 && (
                        <div className="mt-3">
                          <div className="text-sm font-medium text-red-600 mb-1">
                            Errors ({result.errors.length}):
                          </div>
                          <div className="space-y-1">
                            {result.errors
                              .slice(0, 3)
                              .map((error, errorIndex) => (
                                <div
                                  key={errorIndex}
                                  className="text-xs text-red-600 bg-red-50 p-2 rounded"
                                >
                                  {error}
                                </div>
                              ))}
                            {result.errors.length > 3 && (
                              <div className="text-xs text-muted-foreground">
                                ...and {result.errors.length - 3} more errors
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Success Message */}
              {results.totalImported > 0 && (
                <div className="bg-green-50 border border-green-200 text-green-700 p-4 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <CheckCircle className="h-5 w-5" />
                    <span className="font-medium">
                      Import Completed Successfully!
                    </span>
                  </div>
                  <p className="text-sm">
                    {results.totalImported} new recipes have been added to your
                    collection. You can find them on your main recipes page.
                  </p>
                </div>
              )}
            </div>
          )}

          <div className="flex justify-end pt-4">
            <Button onClick={onClose} disabled={isImporting}>
              {isImporting ? 'Importing...' : 'Close'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ImportProgressModal;
