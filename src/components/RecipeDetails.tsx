'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { RecipeWithRssFeed } from '@/lib/supabase';
import { updateRecipe } from '@/lib/recipes';
import {
  scrapeRecipeFromUrl,
  convertScrapedRecipeToDbFormat,
  formatTime,
  formatIngredient,
  formatInstruction,
  ScrapedRecipeData,
} from '@/lib/recipeScraper';
import { Button } from '@/components/ui';

interface RecipeDetailsProps {
  recipe: RecipeWithRssFeed;
  onRecipeUpdated?: (updatedRecipe: RecipeWithRssFeed) => void;
}

const RecipeDetails: React.FC<RecipeDetailsProps> = ({
  recipe,
  onRecipeUpdated,
}) => {
  const [scrapedData, setScrapedData] = useState<ScrapedRecipeData | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasAttemptedScraping, setHasAttemptedScraping] = useState(false);

  const handleScrapeRecipe = useCallback(async () => {
    if (!recipe.recipe_url) return;

    setIsLoading(true);
    setError(null);
    setHasAttemptedScraping(true);

    try {
      const result = await scrapeRecipeFromUrl(recipe.recipe_url);

      if (result.error) {
        setError(result.error);
        return;
      }

      if (result.recipe) {
        setScrapedData(result.recipe);

        // Update the recipe in the database with scraped data
        try {
          const dbData = convertScrapedRecipeToDbFormat(result.recipe, {
            title: recipe.title,
            description: recipe.description,
            recipe_url: recipe.recipe_url,
            user_id: recipe.user_id,
          });

          const updatedRecipe = await updateRecipe(recipe.id, dbData);
          onRecipeUpdated?.(updatedRecipe);
        } catch {
          // Still show the scraped data even if database update fails
        }
      }
    } catch {
      setError('Failed to scrape recipe data');
    } finally {
      setIsLoading(false);
    }
  }, [
    recipe.recipe_url,
    recipe.id,
    recipe.title,
    recipe.description,
    recipe.user_id,
    onRecipeUpdated,
  ]);

  // Check if we already have scraped data or if we should attempt scraping
  useEffect(() => {
    const shouldScrape =
      recipe.recipe_url &&
      !hasAttemptedScraping &&
      (!recipe.ingredients || recipe.ingredients.length === 0) &&
      (!recipe.instructions || recipe.instructions.length === 0);

    if (shouldScrape) {
      handleScrapeRecipe();
    }
  }, [
    recipe.recipe_url,
    recipe.ingredients,
    recipe.instructions,
    hasAttemptedScraping,
    handleScrapeRecipe,
  ]);

  // Use scraped data if available, otherwise use existing recipe data
  const displayData = scrapedData || {
    ingredients: recipe.ingredients,
    instructions: recipe.instructions,
    prepTime: recipe.prep_time_minutes,
    cookTime: recipe.cook_time_minutes,
    totalTime: recipe.total_time_minutes,
    servings: recipe.servings,
    difficulty: recipe.difficulty_level,
    cuisine: recipe.cuisine_type,
  };

  const hasRecipeData =
    (displayData.ingredients && displayData.ingredients.length > 0) ||
    (displayData.instructions && displayData.instructions.length > 0) ||
    displayData.prepTime ||
    displayData.cookTime ||
    displayData.servings;

  if (isLoading) {
    return (
      <div className="bg-card rounded-lg shadow-lg p-6 mt-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-48 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded w-full"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error && !hasRecipeData) {
    return (
      <div className="bg-card rounded-lg shadow-lg p-6 mt-6">
        <div className="text-center">
          <div className="text-red-500 mb-4">
            <svg
              className="w-12 h-12 mx-auto mb-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <p className="text-sm">{error}</p>
          </div>
          {recipe.recipe_url && (
            <Button onClick={handleScrapeRecipe} variant="outline" size="sm">
              Try Again
            </Button>
          )}
        </div>
      </div>
    );
  }

  if (!hasRecipeData && !recipe.recipe_url) {
    return null; // Don't show anything if no recipe URL and no data
  }

  return (
    <div className="bg-card rounded-lg shadow-lg p-6 mt-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-foreground">Recipe Details</h2>
        {recipe.recipe_url && (
          <Button
            onClick={handleScrapeRecipe}
            variant="outline"
            size="sm"
            disabled={isLoading}
          >
            Reload Recipe Details
          </Button>
        )}
      </div>

      {error && hasRecipeData && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-4">
          <p className="text-yellow-800 text-sm">
            Some recipe details may be incomplete: {error}
          </p>
        </div>
      )}

      {/* Recipe Meta Information */}
      {(displayData.prepTime ||
        displayData.cookTime ||
        displayData.servings ||
        displayData.difficulty ||
        displayData.cuisine) && (
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6 p-4 bg-gray-50 rounded-lg">
          {displayData.prepTime && (
            <div className="text-center">
              <div className="text-sm text-gray-600">Prep Time</div>
              <div className="font-semibold">
                {formatTime(displayData.prepTime)}
              </div>
            </div>
          )}
          {displayData.cookTime && (
            <div className="text-center">
              <div className="text-sm text-gray-600">Cook Time</div>
              <div className="font-semibold">
                {formatTime(displayData.cookTime)}
              </div>
            </div>
          )}
          {displayData.totalTime && (
            <div className="text-center">
              <div className="text-sm text-gray-600">Total Time</div>
              <div className="font-semibold">
                {formatTime(displayData.totalTime)}
              </div>
            </div>
          )}
          {displayData.servings && (
            <div className="text-center">
              <div className="text-sm text-gray-600">Servings</div>
              <div className="font-semibold">{displayData.servings}</div>
            </div>
          )}
          {displayData.difficulty && (
            <div className="text-center">
              <div className="text-sm text-gray-600">Difficulty</div>
              <div className="font-semibold capitalize">
                {displayData.difficulty}
              </div>
            </div>
          )}
        </div>
      )}

      <div className="grid md:grid-cols-2 gap-6">
        {/* Ingredients */}
        {displayData.ingredients && displayData.ingredients.length > 0 && (
          <div>
            <h3 className="text-xl font-semibold mb-4 text-foreground">
              Ingredients
            </h3>
            <ul className="space-y-2">
              {displayData.ingredients.map((ingredient, index) => (
                <li key={index} className="flex items-start">
                  <span className="text-primary mr-2 mt-1">•</span>
                  <span className="text-foreground">
                    {formatIngredient(ingredient)}
                  </span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Instructions */}
        {displayData.instructions && displayData.instructions.length > 0 && (
          <div>
            <h3 className="text-xl font-semibold mb-4 text-foreground">
              Instructions
            </h3>
            <ol className="space-y-3">
              {displayData.instructions.map((instruction, index) => (
                <li key={index} className="flex items-start">
                  <span className="bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-sm font-semibold mr-3 mt-0.5 flex-shrink-0">
                    {index + 1}
                  </span>
                  <span className="text-foreground">
                    {formatInstruction(instruction)}
                  </span>
                </li>
              ))}
            </ol>
          </div>
        )}
      </div>

      {displayData.cuisine && (
        <div className="mt-6 pt-4 border-t border-gray-200">
          <span className="text-sm text-gray-600">Cuisine: </span>
          <span className="text-sm font-medium text-foreground capitalize">
            {displayData.cuisine}
          </span>
        </div>
      )}
    </div>
  );
};

export default RecipeDetails;
