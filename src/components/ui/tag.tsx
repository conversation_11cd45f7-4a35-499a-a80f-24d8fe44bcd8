import React from 'react';
import { cn } from '@/lib/utils';

export interface TagProps {
  children: React.ReactNode;
  variant?: 'default' | 'cuisine' | 'ingredient' | 'secondary';
  size?: 'sm' | 'md';
  className?: string;
  onClick?: () => void;
  removable?: boolean;
  onRemove?: () => void;
}

const Tag: React.FC<TagProps> = ({
  children,
  variant = 'default',
  size = 'sm',
  className,
  onClick,
  removable = false,
  onRemove,
}) => {
  const baseClasses = 'inline-flex items-center gap-1 rounded-full font-medium transition-colors';
  
  const variantClasses = {
    default: 'bg-gray-100 text-gray-800 hover:bg-gray-200',
    cuisine: 'bg-blue-100 text-blue-800 hover:bg-blue-200',
    ingredient: 'bg-green-100 text-green-800 hover:bg-green-200',
    secondary: 'bg-gray-200 text-gray-700 hover:bg-gray-300',
  };

  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1.5 text-sm',
  };

  const combinedClasses = cn(
    baseClasses,
    variantClasses[variant],
    sizeClasses[size],
    onClick && 'cursor-pointer',
    className
  );

  return (
    <span className={combinedClasses} onClick={onClick}>
      {children}
      {removable && onRemove && (
        <button
          type="button"
          onClick={(e) => {
            e.stopPropagation();
            onRemove();
          }}
          className="ml-1 hover:bg-black/10 rounded-full p-0.5"
        >
          <svg
            className="h-3 w-3"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      )}
    </span>
  );
};

export default Tag;
