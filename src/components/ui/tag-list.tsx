import React from 'react';
import Tag from './tag';

export interface TagListProps {
  tags: string[];
  variant?: 'default' | 'cuisine' | 'ingredient' | 'secondary';
  size?: 'sm' | 'md';
  maxTags?: number;
  onTagClick?: (tag: string) => void;
  className?: string;
}

const TagList: React.FC<TagListProps> = ({
  tags,
  variant = 'default',
  size = 'sm',
  maxTags,
  onTagClick,
  className,
}) => {
  if (!tags || tags.length === 0) {
    return null;
  }

  const displayTags = maxTags ? tags.slice(0, maxTags) : tags;
  const remainingCount = maxTags && tags.length > maxTags ? tags.length - maxTags : 0;

  return (
    <div className={`flex flex-wrap gap-1 ${className || ''}`}>
      {displayTags.map((tag, index) => (
        <Tag
          key={index}
          variant={variant}
          size={size}
          onClick={onTagClick ? () => onTagClick(tag) : undefined}
        >
          {tag}
        </Tag>
      ))}
      {remainingCount > 0 && (
        <Tag variant="secondary" size={size}>
          +{remainingCount} more
        </Tag>
      )}
    </div>
  );
};

export default TagList;
