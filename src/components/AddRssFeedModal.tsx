'use client';

import React, { useState } from 'react';
import { Button, Input, Textarea } from '@/components/ui';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { CheckCircle, XCircle, Loader2, Eye } from 'lucide-react';
import { RssFeedTestResult } from '@/lib/supabase';

interface AddRssFeedModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: {
    name: string;
    url: string;
    description: string;
  }) => Promise<void>;
  onTestUrl: (url: string) => Promise<RssFeedTestResult>;
}

const AddRssFeedModal: React.FC<AddRssFeedModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  onTestUrl,
}) => {
  const [formData, setFormData] = useState({
    name: '',
    url: '',
    description: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [testResult, setTestResult] = useState<RssFeedTestResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    setTestResult(null);
    setError(null);
  };

  const handleTestUrl = async () => {
    if (!formData.url) {
      setError('Please enter a URL first');
      return;
    }

    setIsTesting(true);
    setTestResult(null);
    setError(null);

    try {
      const result = await onTestUrl(formData.url);
      setTestResult(result);

      // Auto-fill name if not provided and test was successful
      if (result.valid && result.feedTitle && !formData.name) {
        setFormData((prev) => ({ ...prev, name: result.feedTitle || '' }));
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to test URL');
    } finally {
      setIsTesting(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name || !formData.url) {
      setError('Name and URL are required');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      await onSubmit(formData);
      handleClose();
    } catch (err) {
      setError(
        err instanceof Error ? err.message : 'Failed to create RSS feed'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setFormData({ name: '', url: '', description: '' });
    setTestResult(null);
    setError(null);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Add RSS Feed</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="url" className="text-sm font-medium">
              RSS Feed URL *
            </label>
            <div className="flex gap-2">
              <Input
                id="url"
                type="url"
                placeholder="https://example.com/feed.xml"
                value={formData.url}
                onChange={(e) => handleInputChange('url', e.target.value)}
                className="flex-1"
              />
              <Button
                type="button"
                onClick={handleTestUrl}
                disabled={isTesting || !formData.url}
                variant="outline"
                className="flex items-center gap-2"
              >
                {isTesting ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
                Test
              </Button>
            </div>

            {testResult && (
              <div
                className={`p-3 rounded-md border ${
                  testResult.valid
                    ? 'bg-green-50 border-green-200 text-green-700'
                    : 'bg-red-50 border-red-200 text-red-700'
                }`}
              >
                <div className="flex items-center gap-2 mb-2">
                  {testResult.valid ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <XCircle className="h-4 w-4" />
                  )}
                  <span className="font-medium">
                    {testResult.valid ? 'Valid RSS feed!' : 'Invalid RSS feed'}
                  </span>
                </div>

                {testResult.valid ? (
                  <div className="space-y-2">
                    {testResult.feedTitle && (
                      <p>
                        <strong>Feed title:</strong> {testResult.feedTitle}
                      </p>
                    )}
                    {testResult.sampleItems &&
                      testResult.sampleItems.length > 0 && (
                        <div>
                          <p className="font-medium mb-1">
                            Sample recipes found:
                          </p>
                          <ul className="list-disc list-inside space-y-1 text-sm">
                            {testResult.sampleItems
                              .slice(0, 3)
                              .map((item, index) => (
                                <li key={index}>{item.title}</li>
                              ))}
                            {testResult.sampleItems.length > 3 && (
                              <li>
                                ...and {testResult.sampleItems.length - 3} more
                              </li>
                            )}
                          </ul>
                        </div>
                      )}
                  </div>
                ) : (
                  <p>{testResult.error}</p>
                )}
              </div>
            )}
          </div>

          <div className="space-y-2">
            <label htmlFor="name" className="text-sm font-medium">
              Feed Name *
            </label>
            <Input
              id="name"
              placeholder="My Favorite Food Blog"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="description" className="text-sm font-medium">
              Description (optional)
            </label>
            <Textarea
              id="description"
              placeholder="A brief description of this RSS feed..."
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              rows={3}
            />
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
          )}

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || !formData.name || !formData.url}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Creating...
                </>
              ) : (
                'Create Feed'
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddRssFeedModal;
