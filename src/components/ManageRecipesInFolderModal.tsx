'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogFooter,
  Button,
  Input,
} from '@/components/ui';
import { FolderWithRecipeCount, RecipeWithRssFeed } from '@/lib/supabase';
import { Search, Plus, Minus } from 'lucide-react';
import { decodeHtmlEntities } from '@/lib/utils';

interface ManageRecipesInFolderModalProps {
  isOpen: boolean;
  onClose: () => void;
  onRecipesUpdated: () => void;
  folder: FolderWithRecipeCount;
}

const ManageRecipesInFolderModal: React.FC<ManageRecipesInFolderModalProps> = ({
  isOpen,
  onClose,
  onRecipesUpdated,
  folder,
}) => {
  const [allRecipes, setAllRecipes] = useState<RecipeWithRssFeed[]>([]);
  const [recipesInFolder, setRecipesInFolder] = useState<RecipeWithRssFeed[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);

        // Fetch all recipes - we'll use the existing getRecipes function via a client-side call
        // For now, we'll create a simple endpoint or use the existing pattern
        const { getRecipes } = await import('@/lib/recipes');
        const allRecipesData = await getRecipes();

        // Fetch recipes in this folder
        const folderRecipesResponse = await fetch(`/api/folders/${folder.id}/recipes`);
        if (!folderRecipesResponse.ok) {
          throw new Error('Failed to fetch folder recipes');
        }
        const folderRecipesData = await folderRecipesResponse.json();

        setAllRecipes(allRecipesData || []);
        setRecipesInFolder(folderRecipesData.recipes || []);
      } catch (error) {
        // Handle error silently or show user notification
        const message = error instanceof Error ? error.message : 'Unknown error';
        // TODO: Show error toast notification
        void message; // Suppress unused variable warning
      } finally {
        setIsLoading(false);
      }
    };

    if (isOpen) {
      fetchData();
    }
  }, [isOpen, folder.id]);

  const handleAddRecipe = async (recipe: RecipeWithRssFeed) => {
    try {
      setIsUpdating(true);
      
      const response = await fetch(`/api/folders/${folder.id}/recipes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ recipeIds: [recipe.id] }),
      });

      if (!response.ok) {
        throw new Error('Failed to add recipe to folder');
      }

      setRecipesInFolder(prev => [...prev, recipe]);
    } catch (error) {
      // Handle error silently or show user notification
      const message = error instanceof Error ? error.message : 'Unknown error';
      // TODO: Show error toast notification
      void message; // Suppress unused variable warning
    } finally {
      setIsUpdating(false);
    }
  };

  const handleRemoveRecipe = async (recipe: RecipeWithRssFeed) => {
    try {
      setIsUpdating(true);
      
      const response = await fetch(`/api/folders/${folder.id}/recipes`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ recipeIds: [recipe.id] }),
      });

      if (!response.ok) {
        throw new Error('Failed to remove recipe from folder');
      }

      setRecipesInFolder(prev => prev.filter(r => r.id !== recipe.id));
    } catch (error) {
      // Handle error silently or show user notification
      const message = error instanceof Error ? error.message : 'Unknown error';
      // TODO: Show error toast notification
      void message; // Suppress unused variable warning
    } finally {
      setIsUpdating(false);
    }
  };

  const handleClose = () => {
    setSearchTerm('');
    onRecipesUpdated();
    onClose();
  };

  const filteredAllRecipes = allRecipes.filter(recipe =>
    decodeHtmlEntities(recipe.title).toLowerCase().includes(searchTerm.toLowerCase())
  );

  const recipesInFolderIds = new Set(recipesInFolder.map(r => r.id));
  const availableRecipes = filteredAllRecipes.filter(recipe => 
    !recipesInFolderIds.has(recipe.id)
  );

  if (isLoading) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-4xl max-h-[80vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>Manage Recipes in &ldquo;{folder.name}&rdquo;</DialogTitle>
          </DialogHeader>
          <div className="flex items-center justify-center py-8">
            <div className="text-center">Loading recipes...</div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>Manage Recipes in &ldquo;{folder.name}&rdquo;</DialogTitle>
        </DialogHeader>
        
        <div className="flex-1 overflow-hidden flex flex-col">
          {/* Search */}
          <div className="mb-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search recipes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-4 overflow-hidden">
            {/* Recipes in folder */}
            <div className="flex flex-col">
              <h3 className="font-semibold mb-2 text-green-600">
                In Folder ({recipesInFolder.length})
              </h3>
              <div className="flex-1 overflow-y-auto border rounded-lg p-2 space-y-2">
                {recipesInFolder.length === 0 ? (
                  <p className="text-gray-500 text-sm text-center py-4">
                    No recipes in this folder yet
                  </p>
                ) : (
                  recipesInFolder.map((recipe) => (
                    <div
                      key={recipe.id}
                      className="flex items-center justify-between p-2 bg-green-50 rounded border"
                    >
                      <span className="text-sm truncate flex-1">{decodeHtmlEntities(recipe.title)}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveRecipe(recipe)}
                        disabled={isUpdating}
                        className="ml-2 h-6 w-6 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Minus className="h-3 w-3" />
                      </Button>
                    </div>
                  ))
                )}
              </div>
            </div>

            {/* Available recipes */}
            <div className="flex flex-col">
              <h3 className="font-semibold mb-2 text-blue-600">
                Available Recipes ({availableRecipes.length})
              </h3>
              <div className="flex-1 overflow-y-auto border rounded-lg p-2 space-y-2">
                {availableRecipes.length === 0 ? (
                  <p className="text-gray-500 text-sm text-center py-4">
                    {searchTerm ? 'No recipes match your search' : 'All recipes are already in this folder'}
                  </p>
                ) : (
                  availableRecipes.map((recipe) => (
                    <div
                      key={recipe.id}
                      className="flex items-center justify-between p-2 bg-blue-50 rounded border"
                    >
                      <span className="text-sm truncate flex-1">{decodeHtmlEntities(recipe.title)}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleAddRecipe(recipe)}
                        disabled={isUpdating}
                        className="ml-2 h-6 w-6 p-0 text-green-600 hover:text-green-700 hover:bg-green-50"
                      >
                        <Plus className="h-3 w-3" />
                      </Button>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button onClick={handleClose}>Done</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ManageRecipesInFolderModal;
