'use client';

import React from 'react';
import { Button } from '@/components/ui';
import {
  getAllThemes,
  setTheme,
  getStoredTheme,
  type ThemeName,
} from '@/utils/themeManager';

interface ThemePickerProps {
  darkMode: boolean;
}

export default function ThemePicker({ darkMode }: ThemePickerProps) {
  const [currentTheme, setCurrentTheme] = React.useState<ThemeName>('desert');
  const themes = getAllThemes();

  // Load stored theme after hydration
  React.useEffect(() => {
    const storedTheme = getStoredTheme();
    if (storedTheme) {
      setCurrentTheme(storedTheme);
    }
  }, []);

  const handleThemeChange = (themeName: ThemeName) => {
    setTheme(themeName, darkMode);
    setCurrentTheme(themeName);
  };

  // Update theme when dark mode changes
  React.useEffect(() => {
    setTheme(currentTheme, darkMode);
  }, [darkMode, currentTheme]);

  return (
    <div className="flex flex-wrap gap-2 p-2">
      {themes.map((theme) => (
        <Button
          key={theme}
          variant={currentTheme === theme ? 'default' : 'outline'}
          onClick={() => handleThemeChange(theme)}
          className="capitalize"
        >
          {theme}
        </Button>
      ))}
    </div>
  );
}
