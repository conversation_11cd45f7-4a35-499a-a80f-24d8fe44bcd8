'use client';

import React, { useState } from 'react';
import { Plus } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  Button,
} from '@/components/ui';
import { createRecipe } from '@/lib/recipes';
import { useRecipeForm, RecipeFormData } from '@/hooks/useRecipeForm';
import RecipeForm from '@/components/RecipeForm';
import { useAuth } from '@/contexts/AuthContext';

interface AddRecipeModalProps {
  onRecipeAdded?: () => void;
}

const AddRecipeModal: React.FC<AddRecipeModalProps> = ({ onRecipeAdded }) => {
  const [open, setOpen] = useState(false);
  const { user } = useAuth();

  const handleSubmit = async (data: RecipeFormData) => {
    if (!user) {
      throw new Error('User must be authenticated to create recipes');
    }

    await createRecipe({
      title: data.title || 'Untitled Recipe', // Provide default title if empty
      description: data.description || null,
      image_url: data.image_url || null,
      recipe_url: data.recipe_url || null,
      cuisine_type: data.cuisine_type || null,
      user_id: user.id,
    });

    // Reset form and close modal
    resetForm();
    setOpen(false);

    // Notify parent component
    onRecipeAdded?.();
  };

  const handleCancel = () => {
    resetForm();
    setOpen(false);
  };

  const {
    formData,
    errors,
    isLoading,
    isScrapingLoading,
    handleInputChange,
    handleSubmit: onFormSubmit,
    handleScrapeRecipe,
    resetForm,
  } = useRecipeForm({
    onSubmit: handleSubmit,
    onCancel: handleCancel,
  });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="flex items-center justify-center gap-2 w-full" size="sm">
          <Plus className="h-4 w-4" />
          <span className="sm:inline">Add Recipe</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add New Recipe</DialogTitle>
          <DialogDescription>
            Create a new recipe to add to your collection. Fill in the details
            below.
          </DialogDescription>
        </DialogHeader>
        <RecipeForm
          formData={formData}
          errors={errors}
          isLoading={isLoading}
          isScrapingLoading={isScrapingLoading}
          onInputChange={handleInputChange}
          onSubmit={onFormSubmit}
          onCancel={handleCancel}
          onScrapeRecipe={handleScrapeRecipe}
          submitButtonText="Create Recipe"
          loadingButtonText="Creating..."
        />
      </DialogContent>
    </Dialog>
  );
};

export default AddRecipeModal;
