'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui';
import { Clock, Play, Settings, Info } from 'lucide-react';

interface ScheduledImportSettingsProps {
  onManualImport: () => Promise<void>;
  isImporting: boolean;
}

const ScheduledImportSettings: React.FC<ScheduledImportSettingsProps> = ({
  onManualImport,
  isImporting,
}) => {
  const [lastImport, setLastImport] = useState<string | null>(null);
  const [nextScheduledImport, setNextScheduledImport] = useState<string | null>(
    null
  );

  useEffect(() => {
    // Load last import time from localStorage
    const stored = localStorage.getItem('lastRssImport');
    if (stored) {
      setLastImport(stored);
    }

    // Calculate next scheduled import (example: every 6 hours)
    if (stored) {
      const lastTime = new Date(stored);
      const nextTime = new Date(lastTime.getTime() + 6 * 60 * 60 * 1000); // 6 hours
      setNextScheduledImport(nextTime.toISOString());
    }
  }, []);

  const handleManualImport = async () => {
    try {
      await onManualImport();
      const now = new Date().toISOString();
      setLastImport(now);
      localStorage.setItem('lastRssImport', now);

      // Update next scheduled import
      const nextTime = new Date(Date.now() + 6 * 60 * 60 * 1000);
      setNextScheduledImport(nextTime.toISOString());
    } catch {
      // Error handling is done in parent component
    }
  };

  const formatDateTime = (isoString: string | null) => {
    if (!isoString) return 'Never';
    return new Date(isoString).toLocaleString();
  };

  const getTimeUntilNext = () => {
    if (!nextScheduledImport) return null;

    const now = new Date();
    const next = new Date(nextScheduledImport);
    const diff = next.getTime() - now.getTime();

    if (diff <= 0) return 'Overdue';

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  return (
    <div className="bg-card border border-border rounded-lg p-6">
      <div className="flex items-center gap-2 mb-4">
        <Clock className="h-5 w-5 text-primary" />
        <h3 className="text-lg font-semibold">Scheduled Imports</h3>
      </div>

      <div className="space-y-4">
        {/* Import Status */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium text-muted-foreground">
              Last Import:
            </span>
            <div className="mt-1">{formatDateTime(lastImport)}</div>
          </div>
          <div>
            <span className="font-medium text-muted-foreground">
              Next Scheduled:
            </span>
            <div className="mt-1">
              {nextScheduledImport ? (
                <div>
                  <div>{formatDateTime(nextScheduledImport)}</div>
                  <div className="text-xs text-muted-foreground">
                    ({getTimeUntilNext()})
                  </div>
                </div>
              ) : (
                'Not scheduled'
              )}
            </div>
          </div>
        </div>

        {/* Manual Import Button */}
        <div className="flex items-center gap-2">
          <Button
            onClick={handleManualImport}
            disabled={isImporting}
            className="flex items-center gap-2"
          >
            <Play className="h-4 w-4" />
            {isImporting ? 'Importing...' : 'Import Now'}
          </Button>

          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
            onClick={() => {
              // This could open a modal with more detailed scheduling options
              alert(
                'Advanced scheduling settings coming soon! For now, you can set up external cron jobs to call the /api/rss-feeds/cron endpoint.'
              );
            }}
          >
            <Settings className="h-4 w-4" />
            Schedule Settings
          </Button>
        </div>

        {/* Information */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start gap-2">
            <Info className="h-4 w-4 text-blue-600 mt-0.5" />
            <div className="text-sm text-blue-700">
              <div className="font-medium mb-1">Automatic Imports</div>
              <p>
                RSS feeds are automatically checked for new recipes. You can
                also trigger manual imports or set up external cron jobs for
                more control.
              </p>
              <div className="mt-2 text-xs">
                <strong>API Endpoint:</strong> <code>/api/rss-feeds/cron</code>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="border-t pt-4">
          <div className="text-sm text-muted-foreground">
            <div className="font-medium mb-2">Import Frequency</div>
            <ul className="space-y-1 text-xs">
              <li>• Manual imports: Anytime</li>
              <li>• Recommended schedule: Every 6 hours</li>
              <li>• Max items per feed: 10-20 recipes</li>
              <li>• Automatic duplicate detection</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScheduledImportSettings;
