'use client';

import React from 'react';
import { RecipeWithRssFeed } from '@/lib/supabase';
import {
  formatTime,
  formatIngredient,
  formatInstruction,
} from '@/lib/recipeScraper';
import { decodeHtmlEntities } from '@/lib/utils';

interface RecipeDetailsPreviewProps {
  recipe: RecipeWithRssFeed;
}

const RecipeDetailsPreview: React.FC<RecipeDetailsPreviewProps> = ({
  recipe,
}) => {
  // Use existing recipe data for preview
  const displayData = {
    ingredients: recipe.ingredients,
    instructions: recipe.instructions,
    prepTime: recipe.prep_time_minutes,
    cookTime: recipe.cook_time_minutes,
    totalTime: recipe.total_time_minutes,
    servings: recipe.servings,
    difficulty: recipe.difficulty_level,
    cuisine: recipe.cuisine_type,
  };

  const hasRecipeData =
    (displayData.ingredients && displayData.ingredients.length > 0) ||
    (displayData.instructions && displayData.instructions.length > 0) ||
    displayData.prepTime ||
    displayData.cookTime ||
    displayData.servings;

  if (!hasRecipeData) {
    return (
      <div>
        <div className="text-center text-sm text-gray-500 py-4">
          <p>No recipe details available</p>
          {recipe.recipe_url && (
            <p className="mt-1">Click to view and load recipe details</p>
          )}
        </div>
      </div>
    );
  }

  return (
    <div>
      <h4 className="font-bold mb-3 sm:mb-4 text-sm sm:text-base text-foreground line-clamp-2">{decodeHtmlEntities(recipe.title)}</h4>
      <div className="max-w-xs sm:max-w-sm">
        {/* Recipe Meta Information */}
        {(displayData.prepTime ||
          displayData.cookTime ||
          displayData.servings ||
          displayData.difficulty) && (
          <div className="grid grid-cols-2 gap-1 sm:gap-2 mb-3 sm:mb-4 p-2 sm:p-3 bg-gray-50 rounded-lg text-xs">
            {displayData.prepTime && (
              <div className="text-center">
                <div className="text-gray-600 text-xs">Prep</div>
                <div className="font-semibold text-xs">
                  {formatTime(displayData.prepTime)}
                </div>
              </div>
            )}
            {displayData.cookTime && (
              <div className="text-center">
                <div className="text-gray-600">Cook</div>
                <div className="font-semibold">
                  {formatTime(displayData.cookTime)}
                </div>
              </div>
            )}
            {displayData.servings && (
              <div className="text-center">
                <div className="text-gray-600">Serves</div>
                <div className="font-semibold">{displayData.servings}</div>
              </div>
            )}
            {displayData.difficulty && (
              <div className="text-center">
                <div className="text-gray-600">Level</div>
                <div className="font-semibold capitalize">
                  {displayData.difficulty}
                </div>
              </div>
            )}
          </div>
        )}

        <div className="space-y-4">
          {/* Ingredients Preview */}
          {displayData.ingredients && displayData.ingredients.length > 0 && (
            <div>
              <h4 className="text-sm font-semibold mb-2 text-foreground">
                Ingredients
              </h4>
              <ul className="space-y-1 text-xs">
                {displayData.ingredients
                  .slice(0, 4)
                  .map((ingredient, index) => (
                    <li key={index} className="flex items-start">
                      <span className="text-primary mr-1 mt-0.5">•</span>
                      <span className="text-foreground">
                        {formatIngredient(ingredient)}
                      </span>
                    </li>
                  ))}
                {displayData.ingredients.length > 4 && (
                  <li className="text-gray-500 text-xs italic">
                    +{displayData.ingredients.length - 4} more ingredients...
                  </li>
                )}
              </ul>
            </div>
          )}

          {/* Instructions Preview */}
          {displayData.instructions && displayData.instructions.length > 0 && (
            <div>
              <h4 className="text-sm font-semibold mb-2 text-foreground">
                Instructions
              </h4>
              <ol className="space-y-1 text-xs">
                {displayData.instructions
                  .slice(0, 3)
                  .map((instruction, index) => (
                    <li key={index} className="flex items-start">
                      <span className="bg-primary text-primary-foreground rounded-full w-4 h-4 flex items-center justify-center text-xs font-semibold mr-2 mt-0.5 flex-shrink-0">
                        {index + 1}
                      </span>
                      <span className="text-foreground line-clamp-2">
                        {formatInstruction(instruction)}
                      </span>
                    </li>
                  ))}
                {displayData.instructions.length > 3 && (
                  <li className="text-gray-500 text-xs italic ml-6">
                    +{displayData.instructions.length - 3} more steps...
                  </li>
                )}
              </ol>
            </div>
          )}
        </div>

        {displayData.cuisine && (
          <div className="mt-4 pt-3 border-t border-gray-200">
            <span className="text-xs text-gray-600">Cuisine: </span>
            <span className="text-xs font-medium text-foreground capitalize">
              {displayData.cuisine}
            </span>
          </div>
        )}

        <div className="mt-3 pt-3 border-t border-gray-200 text-center">
          <p className="text-xs text-gray-500">Click card for full details</p>
        </div>
      </div>
    </div>
  );
};

export default RecipeDetailsPreview;
