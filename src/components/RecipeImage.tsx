'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { findBestImageForRecipe } from '@/lib/imageExtractor';
import { updateRecipe } from '@/lib/recipes';

interface RecipeImageProps {
  imageUrl?: string | null;
  recipeUrl?: string | null;
  title: string;
  className?: string;
  priority?: boolean;
  recipeId?: number;
}

const RecipeImage: React.FC<RecipeImageProps> = ({
  imageUrl,
  recipeUrl,
  title,
  className = 'relative w-full h-64 md:h-96',
  priority = false,
  recipeId,
}) => {
  const [scrapedImageUrl, setScrapedImageUrl] = useState<string | null>(null);
  const [isLoadingImage, setIsLoadingImage] = useState(false);
  const [imageError, setImageError] = useState(false);

  // Effect to scrape image when imageUrl is missing but recipeUrl exists
  useEffect(() => {
    const scrapeImage = async () => {
      // Only scrape if we don't have an imageUrl, we have a recipeUrl, and we haven't already scraped
      if (
        !imageUrl &&
        recipeUrl &&
        !scrapedImageUrl &&
        !isLoadingImage &&
        !imageError
      ) {
        setIsLoadingImage(true);
        setImageError(false);

        try {
          const foundImage = await findBestImageForRecipe(recipeUrl);
          if (foundImage) {
            setScrapedImageUrl(foundImage);

            // Update the recipe in the database with the scraped image URL
            if (recipeId) {
              try {
                await updateRecipe(recipeId, { image_url: foundImage });
              } catch {
                // Don't set error state for database update failures - still show the image
              }
            }
          } else {
            setImageError(true);
          }
        } catch {
          setImageError(true);
        } finally {
          setIsLoadingImage(false);
        }
      }
    };

    scrapeImage();
  }, [
    imageUrl,
    recipeUrl,
    scrapedImageUrl,
    isLoadingImage,
    imageError,
    recipeId,
  ]);

  // Determine which image to show
  const displayImageUrl = imageUrl || scrapedImageUrl;

  // If we have an image URL or are loading, show the image container
  if (displayImageUrl || isLoadingImage) {
    return (
      <div className={className}>
        {isLoadingImage ? (
          <div className="w-full h-full bg-gray-200 animate-pulse flex items-center justify-center">
            <div className="text-gray-500 text-sm">Loading image...</div>
          </div>
        ) : (
          <Image
            src={displayImageUrl || '/images/placeholder.jpg'}
            alt={title}
            fill
            style={{ objectFit: 'cover' }}
            priority={priority}
            onError={() => {
              // If the scraped image fails to load, mark it as error
              if (scrapedImageUrl && !imageUrl) {
                setImageError(true);
                setScrapedImageUrl(null);
              }
            }}
          />
        )}
      </div>
    );
  }

  // If no image and no recipe URL to scrape from, don't render anything
  if (!recipeUrl) {
    return null;
  }

  // If we have a recipe URL but failed to find an image, show placeholder
  if (imageError) {
    return (
      <div className={className}>
        <div className="w-full h-full bg-gray-100 flex items-center justify-center">
          <div className="text-gray-500 text-sm text-center">
            <div className="mb-2">📷</div>
            <div>No image found</div>
          </div>
        </div>
      </div>
    );
  }

  // Default: don't render anything if no image and still checking
  return null;
};

export default RecipeImage;
