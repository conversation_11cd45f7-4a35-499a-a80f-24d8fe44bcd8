'use client';

import React from 'react';
import { Button, Input, Textarea, Select } from '@/components/ui';
import {
  FormFields,
  RecipeFormData,
  RecipeFormErrors,
} from '@/hooks/useRecipeForm';
import { COMMON_CUISINE_TYPES } from '@/lib/tagUtils';

export interface RecipeFormProps {
  formData: RecipeFormData;
  errors: RecipeFormErrors;
  isLoading: boolean;
  isScrapingLoading?: boolean;
  onInputChange: (field: FormFields, value: string) => void;
  onSubmit: (e: React.FormEvent) => void;
  onCancel: () => void;
  onScrapeRecipe?: () => void;
  submitButtonText: string;
  loadingButtonText: string;
  idPrefix?: string;
}

const RecipeForm: React.FC<RecipeFormProps> = ({
  formData,
  errors,
  isLoading,
  isScrapingLoading = false,
  onInputChange,
  onSubmit,
  onCancel,
  onScrapeRecipe,
  submitButtonText,
  loadingButtonText,
  idPrefix = '',
}) => {
  const getFieldId = (field: string) => {
    return idPrefix ? `${idPrefix}-${field}` : field;
  };

  return (
    <form onSubmit={onSubmit} className="space-y-4">
      <div className="space-y-2">
        <label
          htmlFor={getFieldId('recipe_url')}
          className="text-sm font-medium"
        >
          Recipe URL *
        </label>
        <div className="flex gap-2">
          <Input
            id={getFieldId('recipe_url')}
            placeholder="https://example.com/recipe"
            value={formData.recipe_url || ''}
            onChange={(e) => onInputChange('recipe_url', e.target.value)}
            className={errors.recipe_url ? 'border-red-500' : ''}
          />
          {onScrapeRecipe && (
            <Button
              type="button"
              variant="outline"
              onClick={onScrapeRecipe}
              disabled={
                isLoading || isScrapingLoading || !formData.recipe_url?.trim()
              }
              className="whitespace-nowrap"
            >
              {isScrapingLoading ? 'Scraping recipe...' : 'Scrape Recipe'}
            </Button>
          )}
        </div>
        {errors.recipe_url && (
          <p className="text-sm text-red-500">{errors.recipe_url}</p>
        )}
      </div>

      <div className="space-y-2">
        <label htmlFor={getFieldId('title')} className="text-sm font-medium">
          Title (optional)
        </label>
        <Input
          id={getFieldId('title')}
          placeholder="Enter recipe title (will be auto-filled if empty)"
          value={formData.title || ''}
          onChange={(e) => onInputChange('title', e.target.value)}
          className={errors.title ? 'border-red-500' : ''}
        />
        {errors.title && <p className="text-sm text-red-500">{errors.title}</p>}
      </div>

      <div className="space-y-2">
        <label
          htmlFor={getFieldId('description')}
          className="text-sm font-medium"
        >
          Description (optional)
        </label>
        <Textarea
          id={getFieldId('description')}
          placeholder="Enter recipe description"
          value={formData.description || ''}
          onChange={(e) => onInputChange('description', e.target.value)}
          className={errors.description ? 'border-red-500' : ''}
          rows={3}
        />
        {errors.description && (
          <p className="text-sm text-red-500">{errors.description}</p>
        )}
      </div>

      <div className="space-y-2">
        <label
          htmlFor={getFieldId('image_url')}
          className="text-sm font-medium"
        >
          Image URL (optional)
        </label>
        <Input
          id={getFieldId('image_url')}
          placeholder="https://example.com/image.jpg"
          value={formData.image_url || ''}
          onChange={(e) => onInputChange('image_url', e.target.value)}
          className={errors.image_url ? 'border-red-500' : ''}
        />
        {errors.image_url && (
          <p className="text-sm text-red-500">{errors.image_url}</p>
        )}
      </div>

      <div className="space-y-2">
        <label
          htmlFor={getFieldId('cuisine_type')}
          className="text-sm font-medium"
        >
          Cuisine Type (optional)
        </label>
        <Select
          id={getFieldId('cuisine_type')}
          value={formData.cuisine_type || ''}
          onChange={(e) => onInputChange('cuisine_type', e.target.value)}
          className={errors.cuisine_type ? 'border-red-500' : ''}
        >
          <option value="" disabled>
            Select cuisine type
          </option>
          {COMMON_CUISINE_TYPES.map((cuisine) => (
            <option key={cuisine} value={cuisine}>
              {cuisine}
            </option>
          ))}
        </Select>
        {errors.cuisine_type && (
          <p className="text-sm text-red-500">{errors.cuisine_type}</p>
        )}
      </div>

      <div className="flex justify-end space-x-2">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? loadingButtonText : submitButtonText}
        </Button>
      </div>
    </form>
  );
};

export default RecipeForm;
