'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui';
import { User, LogOut, ChevronDown, Settings, Rss } from 'lucide-react';

export default function UserMenu() {
  const { user, signOut, loading } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const router = useRouter();

  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="h-8 w-20 bg-muted rounded"></div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  const handleSignOut = async () => {
    await signOut(() => {
      router.push('/login');
    });
    setIsOpen(false);
  };

  const handleSettingsClick = () => {
    router.push('/settings');
    setIsOpen(false);
  };

  const handleRssFeedsClick = () => {
    router.push('/rss-feeds');
    setIsOpen(false);
  };

  return (
    <div className="relative">
      <Button
        variant="ghost"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-1 sm:gap-2 p-2 sm:px-3"
        size="sm"
      >
        <User className="h-4 w-4" />
        <span className="hidden sm:inline text-sm">
          {user.email?.split('@')[0] || 'User'}
        </span>
        <ChevronDown className="h-3 w-3 sm:h-4 sm:w-4" />
      </Button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />

          {/* Dropdown menu */}
          <div className="absolute right-0 mt-2 w-48 sm:w-52 bg-card border border-border rounded-md shadow-lg z-20">
            <div className="py-1">
              <div className="px-4 py-2 text-xs sm:text-sm text-foreground/60 border-b border-border truncate">
                {user.email}
              </div>
              <button
                onClick={handleRssFeedsClick}
                className="flex items-center gap-2 w-full px-4 py-2 text-sm text-foreground hover:bg-primary hover:text-white transition-colors"
              >
                <Rss className="h-4 w-4" />
                RSS Feeds
              </button>
              <button
                onClick={handleSettingsClick}
                className="flex items-center gap-2 w-full px-4 py-2 text-sm text-foreground hover:bg-primary hover:text-white transition-colors"
              >
                <Settings className="h-4 w-4" />
                Settings
              </button>
              <button
                onClick={handleSignOut}
                className="flex items-center gap-2 w-full px-4 py-2 text-sm text-foreground hover:bg-primary hover:text-white transition-colors"
              >
                <LogOut className="h-4 w-4" />
                Sign out
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
