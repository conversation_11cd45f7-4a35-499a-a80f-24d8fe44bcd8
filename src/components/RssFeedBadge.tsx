import React from 'react';
import { cn } from '@/lib/utils';

export interface RssFeedBadgeProps {
  feedName: string;
  feedUrl?: string;
  size?: 'sm' | 'md';
  className?: string;
  showIcon?: boolean;
}

const RssFeedBadge: React.FC<RssFeedBadgeProps> = ({
  feedName,
  feedUrl,
  size = 'sm',
  className,
  showIcon = true,
}) => {
  const baseClasses = 'inline-flex items-center gap-1 rounded-full font-medium transition-colors';
  
  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1.5 text-sm',
  };

  const iconSizeClasses = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
  };

  const combinedClasses = cn(
    baseClasses,
    'bg-orange-100 text-orange-800 hover:bg-orange-200',
    sizeClasses[size],
    className
  );

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (feedUrl) {
      window.open(feedUrl, '_blank', 'noopener,noreferrer');
    }
  };

  return (
    <span 
      className={cn(combinedClasses, feedUrl && 'cursor-pointer')} 
      onClick={feedUrl ? handleClick : undefined}
      title={feedUrl ? `From RSS feed: ${feedName}` : `From RSS feed: ${feedName}`}
    >
      {showIcon && (
        <svg
          className={iconSizeClasses[size]}
          fill="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M3.429 14.571a1.714 1.714 0 1 1 3.428 0 1.714 1.714 0 0 1-3.428 0zM3.429 3.429v3.428c7.571 0 13.714 6.143 13.714 13.714h3.428C20.571 13.143 10.857 3.429 3.429 3.429zM3.429 10.286v3.428c3.786 0 6.857 3.071 6.857 6.857h3.428c0-5.714-4.571-10.285-10.285-10.285z"/>
        </svg>
      )}
      RSS: {feedName}
    </span>
  );
};

export default RssFeedBadge;
