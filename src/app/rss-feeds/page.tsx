'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui';
import { useRssFeeds, ImportSummary } from '@/hooks/useRssFeeds';
import RssFeedList from '@/components/RssFeedList';
import AddRssFeedModal from '@/components/AddRssFeedModal';
import ImportProgressModal from '@/components/ImportProgressModal';
import ScheduledImportSettings from '@/components/ScheduledImportSettings';
import { Plus, Download, RefreshCw } from 'lucide-react';

export default function RssFeedsPage() {
  const {
    feeds,
    isLoading,
    isImporting,
    error,
    fetchFeeds,
    createFeed,
    updateFeed,
    deleteFeed,
    toggleFeedStatus,
    testFeedUrl,
    importFromFeed,
    importFromAllFeeds,
  } = useRssFeeds();

  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);
  const [importResults, setImportResults] = useState<ImportSummary | null>(
    null
  );

  const handleAddFeed = async (data: {
    name: string;
    url: string;
    description: string;
  }) => {
    try {
      await createFeed(data);
      setIsAddModalOpen(false);
    } catch (error) {
      // Error is handled by the hook
      throw error;
    }
  };

  const handleImportAll = async () => {
    try {
      setIsImportModalOpen(true);
      const results = await importFromAllFeeds({
        maxItems: 20,
        skipDuplicates: true,
        autoScrapeImages: true,
      });
      setImportResults(results);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Import failed:', error);
    }
  };

  const handleImportSingle = async (feedId: number) => {
    try {
      const result = await importFromFeed(feedId, {
        maxItems: 20,
        skipDuplicates: true,
        autoScrapeImages: true,
      });

      // Show success message or handle result
      // eslint-disable-next-line no-console
      console.log('Import completed:', result);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Import failed:', error);
    }
  };

  const activeFeeds = feeds.filter((feed) => feed.is_active);
  const inactiveFeeds = feeds.filter((feed) => !feed.is_active);

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-7xl mx-auto py-4 px-4 sm:py-6 sm:px-6 lg:px-8">
        <div className="mb-6 sm:mb-8">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4 mb-4">
            <div className="flex-1">
              <h1 className="text-2xl sm:text-3xl font-bold text-foreground">RSS Feeds</h1>
              <p className="text-muted-foreground mt-2 text-sm sm:text-base">
                Manage your RSS feeds and automatically import recipes from your
                favorite food blogs.
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-2 sm:gap-2">
              <Button
                onClick={() => fetchFeeds()}
                variant="outline"
                disabled={isLoading}
                className="flex items-center justify-center gap-2 w-full sm:w-auto"
                size="sm"
              >
                <RefreshCw
                  className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`}
                />
                <span className="sm:inline">Refresh</span>
              </Button>
              <Button
                onClick={handleImportAll}
                disabled={isImporting || activeFeeds.length === 0}
                className="flex items-center justify-center gap-2 w-full sm:w-auto"
                size="sm"
              >
                <Download className="h-4 w-4" />
                <span className="sm:inline">Import All</span>
              </Button>
              <Button
                onClick={() => setIsAddModalOpen(true)}
                className="flex items-center justify-center gap-2 w-full sm:w-auto"
                size="sm"
              >
                <Plus className="h-4 w-4" />
                <span className="sm:inline">Add Feed</span>
              </Button>
            </div>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}

          {feeds.length === 0 && !isLoading && (
            <div className="text-center py-12">
              <div className="text-muted-foreground mb-4">
                <Download className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <h3 className="text-lg font-medium mb-2">No RSS feeds yet</h3>
                <p>
                  Add your first RSS feed to start automatically importing
                  recipes.
                </p>
              </div>
              <Button
                onClick={() => setIsAddModalOpen(true)}
                className="flex items-center gap-2 mx-auto"
              >
                <Plus className="h-4 w-4" />
                Add Your First Feed
              </Button>
            </div>
          )}
        </div>

        {feeds.length > 0 && (
          <div className="space-y-8">
            {/* Scheduled Import Settings */}
            <ScheduledImportSettings
              onManualImport={handleImportAll}
              isImporting={isImporting}
            />

            {activeFeeds.length > 0 && (
              <div>
                <h2 className="text-xl font-semibold mb-4 text-foreground">
                  Active Feeds ({activeFeeds.length})
                </h2>
                <RssFeedList
                  feeds={activeFeeds}
                  onUpdate={updateFeed}
                  onDelete={deleteFeed}
                  onToggleStatus={toggleFeedStatus}
                  onImport={handleImportSingle}
                  isImporting={isImporting}
                />
              </div>
            )}

            {inactiveFeeds.length > 0 && (
              <div>
                <h2 className="text-xl font-semibold mb-4 text-muted-foreground">
                  Inactive Feeds ({inactiveFeeds.length})
                </h2>
                <RssFeedList
                  feeds={inactiveFeeds}
                  onUpdate={updateFeed}
                  onDelete={deleteFeed}
                  onToggleStatus={toggleFeedStatus}
                  onImport={handleImportSingle}
                  isImporting={isImporting}
                />
              </div>
            )}
          </div>
        )}
      </div>

      <AddRssFeedModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSubmit={handleAddFeed}
        onTestUrl={testFeedUrl}
      />

      <ImportProgressModal
        isOpen={isImportModalOpen}
        onClose={() => {
          setIsImportModalOpen(false);
          setImportResults(null);
        }}
        results={importResults}
        isImporting={isImporting}
      />
    </div>
  );
}
