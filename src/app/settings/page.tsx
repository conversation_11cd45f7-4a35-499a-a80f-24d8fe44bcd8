'use client';

import { useDarkMode } from '@/hooks/useDarkMode';
import ThemePicker from '@/components/ThemePicker';
import DarkModeToggle from '@/components/DarkModeToggle';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui';

export default function SettingsPage() {
  const [darkMode, setDarkMode] = useDarkMode();

  return (
    <main className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
      <div className="px-4 py-6 sm:px-0">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-primary">Settings</h1>
          <p className="text-foreground/60 mt-2">
            Customize your Recipe Picker experience
          </p>
        </div>

        <div className="space-y-6">
          {/* Appearance Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Appearance</CardTitle>
              <CardDescription>
                Customize how Recipe Picker looks and feels
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Dark Mode Toggle */}
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium text-foreground">Dark Mode</h3>
                  <p className="text-sm text-foreground/60">
                    Toggle between light and dark themes
                  </p>
                </div>
                <DarkModeToggle
                  darkMode={darkMode}
                  setDarkMode={setDarkMode}
                />
              </div>

              {/* Theme Picker */}
              <div>
                <h3 className="text-sm font-medium text-foreground mb-2">Color Theme</h3>
                <p className="text-sm text-foreground/60 mb-4">
                  Choose your preferred color scheme
                </p>
                <ThemePicker darkMode={darkMode} />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </main>
  );
}
