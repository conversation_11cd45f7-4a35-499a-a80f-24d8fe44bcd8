'use client';

import { useState, useEffect, useMemo, useCallback } from 'react';
import { <PERSON><PERSON>, <PERSON>, CardHeader, CardT<PERSON>le, CardContent, Input } from '@/components/ui';
import { FolderWithRecipeCount } from '@/lib/supabase';
import { Plus, Edit2, Trash2, FolderOpen, Star, Search, SortAsc, SortDesc, RefreshCw, Grid, List, Clock } from 'lucide-react';
import CreateFolderModal from '@/components/CreateFolderModal';
import EditFolderModal from '@/components/EditFolderModal';
import DeleteConfirmationModal from '@/components/DeleteConfirmationModal';
import ManageRecipesInFolderModal from '@/components/ManageRecipesInFolderModal';
import Link from 'next/link';

type SortOption = 'name' | 'recipe_count' | 'created_at';
type SortDirection = 'asc' | 'desc';

export default function FoldersPage() {
  const [folders, setFolders] = useState<FolderWithRecipeCount[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingFolder, setEditingFolder] = useState<FolderWithRecipeCount | null>(null);
  const [deletingFolder, setDeletingFolder] = useState<FolderWithRecipeCount | null>(null);
  const [managingFolder, setManagingFolder] = useState<FolderWithRecipeCount | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<SortOption>('name');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedFolders, setSelectedFolders] = useState<string[]>([]);
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchFolders = useCallback(async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setIsRefreshing(true);
      } else {
        setIsLoading(true);
      }
      setError(null);

      const response = await fetch('/api/folders', {
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch folders: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      setFolders(data.folders || []);
      setLastUpdated(new Date());

      // Clear any selected folders when refreshing
      if (isRefresh) {
        setSelectedFolders([]);
        setShowBulkActions(false);
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Unknown error occurred';
      setError(message);
      console.error('Error fetching folders:', error);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, []);

  useEffect(() => {
    fetchFolders();
  }, []);

  const handleFolderCreated = (newFolder: FolderWithRecipeCount) => {
    setFolders((prev: FolderWithRecipeCount[]) => [...prev, { ...newFolder, recipe_count: 0 }]);
    setIsCreateModalOpen(false);
  };

  const handleFolderUpdated = (updatedFolder: FolderWithRecipeCount) => {
    setFolders((prev: FolderWithRecipeCount[]) =>
      prev.map((folder: FolderWithRecipeCount) =>
        folder.id === updatedFolder.id ? updatedFolder : folder
      )
    );
    setEditingFolder(null);
  };

  const handleDeleteConfirm = async () => {
    if (!deletingFolder) return;

    try {
      setIsDeleting(true);
      const response = await fetch(`/api/folders/${deletingFolder.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete folder');
      }

      setFolders((prev: FolderWithRecipeCount[]) =>
        prev.filter((folder: FolderWithRecipeCount) => folder.id !== deletingFolder.id)
      );
      setDeletingFolder(null);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Unknown error occurred';
      setError(message);
      console.error('Error deleting folder:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleRecipesUpdated = () => {
    // Refresh folders to update recipe counts
    fetchFolders();
    setManagingFolder(null);
  };

  // Filter and sort folders
  const filteredAndSortedFolders = useMemo(() => {
    let filtered = folders.filter(folder =>
      folder.name.toLowerCase().includes(searchQuery.toLowerCase())
    );

    filtered.sort((a, b) => {
      let aValue: string | number;
      let bValue: string | number;

      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'recipe_count':
          aValue = a.recipe_count;
          bValue = b.recipe_count;
          break;
        case 'created_at':
          aValue = new Date(a.created_at || '').getTime();
          bValue = new Date(b.created_at || '').getTime();
          break;
        default:
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
      }

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [folders, searchQuery, sortBy, sortDirection]);

  const toggleSort = (option: SortOption) => {
    if (sortBy === option) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(option);
      setSortDirection('asc');
    }
  };

  // Bulk selection handlers
  const toggleFolderSelection = (folderId: string) => {
    setSelectedFolders(prev => {
      const newSelection = prev.includes(folderId)
        ? prev.filter(id => id !== folderId)
        : [...prev, folderId];

      setShowBulkActions(newSelection.length > 0);
      return newSelection;
    });
  };

  const selectAllFolders = () => {
    const allIds = filteredAndSortedFolders.map(folder => folder.id);
    setSelectedFolders(allIds);
    setShowBulkActions(true);
  };

  const clearSelection = () => {
    setSelectedFolders([]);
    setShowBulkActions(false);
  };

  const handleBulkDelete = async () => {
    if (selectedFolders.length === 0) return;

    try {
      setIsLoading(true);
      const deletePromises = selectedFolders.map(folderId =>
        fetch(`/api/folders/${folderId}`, { method: 'DELETE' })
      );

      const results = await Promise.allSettled(deletePromises);
      const failedDeletes = results.filter(result => result.status === 'rejected');

      if (failedDeletes.length > 0) {
        setError(`Failed to delete ${failedDeletes.length} folder(s)`);
      }

      // Refresh folders list
      await fetchFolders(true);
      clearSelection();
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Bulk delete failed';
      setError(message);
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-refresh every 5 minutes
  useEffect(() => {
    const interval = setInterval(() => {
      if (!isLoading && !isRefreshing) {
        fetchFolders(true);
      }
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, [fetchFolders, isLoading, isRefreshing]);

  if (isLoading) {
    return (
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="text-center">Loading folders...</div>
        </div>
      </main>
    );
  }

  if (error) {
    return (
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="text-center">
            <p className="text-red-600 mb-4">Error: {error}</p>
            <Button onClick={fetchFolders}>Try Again</Button>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div className="px-4 py-6 sm:px-0">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4 mb-6">
          <div>
            <h1 className="text-3xl font-bold text-primary mb-2">Manage Folders</h1>
            <p className="text-foreground/60">
              Organize your recipes into folders for better management
            </p>
            {lastUpdated && (
              <p className="text-sm text-foreground/40 mt-1">
                Last updated: {lastUpdated.toLocaleTimeString()}
              </p>
            )}
          </div>
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => fetchFolders(true)}
              disabled={isRefreshing}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <div className="flex gap-1 border rounded-md p-1">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('grid')}
                className="h-8 w-8 p-0"
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('list')}
                className="h-8 w-8 p-0"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
            <Link href="/?filter=favorites">
              <Button variant="outline" className="flex items-center gap-2">
                <Star className="h-4 w-4" />
                View Favorites
              </Button>
            </Link>
            <Button
              onClick={() => setIsCreateModalOpen(true)}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Create Folder
            </Button>
          </div>
        </div>

        {/* Search and Sort Controls */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-foreground/40" />
            <Input
              placeholder="Search folders..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => toggleSort('name')}
              className="flex items-center gap-2"
            >
              Name
              {sortBy === 'name' && (
                sortDirection === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />
              )}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => toggleSort('recipe_count')}
              className="flex items-center gap-2"
            >
              Recipes
              {sortBy === 'recipe_count' && (
                sortDirection === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        {folders.length === 0 ? (
          <div className="text-center py-12">
            <FolderOpen className="h-16 w-16 text-foreground/40 mx-auto mb-4" />
            <p className="text-foreground/60 mb-4">
              No folders yet. Create your first folder to organize your recipes!
            </p>
            <Button
              onClick={() => setIsCreateModalOpen(true)}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Create Your First Folder
            </Button>
          </div>
        ) : filteredAndSortedFolders.length === 0 ? (
          <div className="text-center py-12">
            <Search className="h-16 w-16 text-foreground/40 mx-auto mb-4" />
            <p className="text-foreground/60 mb-4">
              No folders match your search criteria.
            </p>
            <Button
              variant="outline"
              onClick={() => setSearchQuery('')}
              className="flex items-center gap-2"
            >
              Clear Search
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {filteredAndSortedFolders.map((folder) => (
              <Card key={folder.id} className="h-full flex flex-col">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="truncate">{folder.name}</span>
                    <div className="flex gap-1 ml-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setEditingFolder(folder)}
                        className="h-8 w-8 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                      >
                        <Edit2 className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setDeletingFolder(folder)}
                        className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent className="flex-grow flex flex-col justify-between">
                  <div>
                    <p className="text-sm text-foreground/60 mb-4">
                      {folder.recipe_count} {folder.recipe_count === 1 ? 'recipe' : 'recipes'}
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    onClick={() => setManagingFolder(folder)}
                    className="w-full"
                  >
                    Manage Recipes
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Modals */}
        <CreateFolderModal
          isOpen={isCreateModalOpen}
          onClose={() => setIsCreateModalOpen(false)}
          onFolderCreated={handleFolderCreated}
        />

        {editingFolder && (
          <EditFolderModal
            isOpen={!!editingFolder}
            onClose={() => setEditingFolder(null)}
            onFolderUpdated={handleFolderUpdated}
            folder={editingFolder}
          />
        )}

        {deletingFolder && (
          <DeleteConfirmationModal
            isOpen={!!deletingFolder}
            onClose={() => setDeletingFolder(null)}
            onConfirm={handleDeleteConfirm}
            title={`folder "${deletingFolder.name}"`}
            isLoading={isDeleting}
          />
        )}

        {managingFolder && (
          <ManageRecipesInFolderModal
            isOpen={!!managingFolder}
            onClose={() => setManagingFolder(null)}
            onRecipesUpdated={handleRecipesUpdated}
            folder={managingFolder}
          />
        )}
      </div>
    </main>
  );
}
