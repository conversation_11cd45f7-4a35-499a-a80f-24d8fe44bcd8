'use client';

import Link from 'next/link';

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic';
import { useEffect, useState } from 'react';
import { useParams, notFound, useRouter } from 'next/navigation';
import { getRecipe, deleteRecipe } from '@/lib/recipes';
import { RecipeWithRssFeed } from '@/lib/supabase';
import RecipeImage from '@/components/RecipeImage';
import EditRecipeModal from '@/components/EditRecipeModal';
import DeleteConfirmationModal from '@/components/DeleteConfirmationModal';
import RecipeDetails from '@/components/RecipeDetails';
import RssFeedBadge from '@/components/RssFeedBadge';
import { Button } from '@/components/ui';
import { decodeHtmlEntities } from '@/lib/utils';

export default function CardPage() {
  const params = useParams();
  const router = useRouter();
  const [recipe, setRecipe] = useState<RecipeWithRssFeed | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    const fetchRecipe = async () => {
      try {
        const id = params.id as string;
        const fetchedRecipe = await getRecipe(parseInt(id));

        if (!fetchedRecipe) {
          notFound();
          return;
        }

        setRecipe(fetchedRecipe);
      } catch {
        notFound();
      } finally {
        setIsLoading(false);
      }
    };

    fetchRecipe();
  }, [params.id]);

  const handleEditClick = () => {
    setIsEditModalOpen(true);
  };

  const handleEditClose = () => {
    setIsEditModalOpen(false);
  };

  const handleRecipeUpdated = (updatedRecipe: RecipeWithRssFeed) => {
    setRecipe(updatedRecipe);
    setIsEditModalOpen(false);
  };

  const handleDeleteClick = () => {
    setIsDeleteModalOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!recipe) return;

    try {
      setIsDeleting(true);
      await deleteRecipe(recipe.id);
      // Redirect to home page after successful deletion
      router.push('/');
    } catch {
      // You might want to show a toast notification here
    } finally {
      setIsDeleting(false);
    }
  };

  const handleDeleteCancel = () => {
    setIsDeleteModalOpen(false);
  };

  if (isLoading) {
    return (
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-32 mb-6"></div>
            <div className="bg-gray-200 rounded-lg h-96"></div>
          </div>
        </div>
      </main>
    );
  }

  if (!recipe) {
    notFound();
    return null;
  }

  return (
    <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div className="px-4 py-6 sm:px-0">
        <div className="mb-6">
          <Link href="/" className="text-accent inline-block hover:underline">
            &larr; Back to Recipes
          </Link>
        </div>

        <div className="bg-card rounded-lg shadow-lg overflow-hidden">
          <div className="relative">
            <RecipeImage
              imageUrl={recipe.image_url}
              recipeUrl={recipe.recipe_url}
              title={recipe.title}
              priority
              recipeId={recipe.id}
            />
            <div className="absolute top-4 right-4 flex gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDeleteClick}
                className="h-10 w-10 p-0 bg-white/90 text-red-600 hover:text-red-700 hover:bg-white shadow-md"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                  />
                </svg>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleEditClick}
                className="h-10 w-10 p-0 bg-white/90 text-blue-600 hover:text-blue-700 hover:bg-white shadow-md"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                  />
                </svg>
              </Button>
            </div>
          </div>

          <div className="p-6">
            <h1 className="text-3xl font-bold mb-4 text-foreground">
              {decodeHtmlEntities(recipe.title)}
            </h1>

            {recipe.description && (
              <p className="text-foreground/80 text-lg leading-relaxed mb-6">
                {decodeHtmlEntities(recipe.description)}
              </p>
            )}

            {/* RSS Feed Badge */}
            {recipe.rss_feeds && (
              <div className="mb-6">
                <RssFeedBadge
                  feedName={recipe.rss_feeds.name}
                  feedUrl={recipe.rss_feeds.url}
                  size="md"
                />
              </div>
            )}

            {recipe.recipe_url && (
              <div className="mb-6">
                <a
                  href={recipe.recipe_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
                >
                  View Original Recipe
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 ml-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                    />
                  </svg>
                </a>
              </div>
            )}

            <div className="text-sm text-foreground/60">
              <p>Created: {new Date(recipe.created_at).toLocaleDateString()}</p>
              {recipe.updated_at !== recipe.created_at && (
                <p>
                  Updated: {new Date(recipe.updated_at).toLocaleDateString()}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Recipe Details Component */}
        <RecipeDetails recipe={recipe} onRecipeUpdated={handleRecipeUpdated} />
      </div>

      <EditRecipeModal
        isOpen={isEditModalOpen}
        onClose={handleEditClose}
        onRecipeUpdated={handleRecipeUpdated}
        recipe={recipe}
      />

      <DeleteConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title={decodeHtmlEntities(recipe.title)}
        isLoading={isDeleting}
      />
    </main>
  );
}
