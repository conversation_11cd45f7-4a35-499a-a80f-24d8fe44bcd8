import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { createRecipe } from '@/lib/recipes';
import { scrapeRecipeFromUrlServerSide } from '@/lib/recipeScraper';
import { RecipeInsert } from '@/lib/supabase';

interface BulkImportRequest {
  urls: string[];
  autoScrape?: boolean;
}

interface BulkImportResult {
  success: boolean;
  imported: number;
  failed: number;
  results: Array<{
    url: string;
    success: boolean;
    recipe?: RecipeInsert;
    error?: string;
  }>;
}

// POST /api/recipes/bulk-import - Import multiple recipes from URLs
export async function POST(request: NextRequest) {
  try {
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll() {
            // No-op for server-side
          },
        },
      }
    );

    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body: BulkImportRequest = await request.json();
    const { urls, autoScrape = true } = body;

    // Validate input
    if (!urls || !Array.isArray(urls) || urls.length === 0) {
      return NextResponse.json(
        { error: 'URLs array is required and must not be empty' },
        { status: 400 }
      );
    }

    if (urls.length > 50) {
      return NextResponse.json(
        { error: 'Maximum 50 URLs allowed per request' },
        { status: 400 }
      );
    }

    // Validate URLs
    const validUrls = urls.filter((url) => {
      try {
        new URL(url.trim());
        return true;
      } catch {
        return false;
      }
    });

    if (validUrls.length === 0) {
      return NextResponse.json(
        { error: 'No valid URLs provided' },
        { status: 400 }
      );
    }

    const results: BulkImportResult['results'] = [];
    let imported = 0;
    let failed = 0;

    // Process each URL
    for (const url of validUrls) {
      try {
        let recipeData: Partial<RecipeInsert> = {
          recipe_url: url.trim(),
          user_id: user.id,
        };

        // Auto-scrape recipe data if enabled
        if (autoScrape) {
          const scrapingResult = await scrapeRecipeFromUrlServerSide(
            url.trim()
          );

          if (scrapingResult.recipe && !scrapingResult.error) {
            recipeData = {
              ...recipeData,
              title:
                scrapingResult.recipe.title ||
                `Recipe from ${new URL(url).hostname}`,
              description: scrapingResult.recipe.description || null,
              image_url: scrapingResult.recipe.image || null,
              ingredients: scrapingResult.recipe.ingredients || null,
              instructions: scrapingResult.recipe.instructions || null,
              prep_time_minutes: scrapingResult.recipe.prepTime || null,
              cook_time_minutes: scrapingResult.recipe.cookTime || null,
              total_time_minutes: scrapingResult.recipe.totalTime || null,
              servings: scrapingResult.recipe.servings || null,
              cuisine_type: scrapingResult.recipe.cuisine || null,
              scraped_data: scrapingResult.recipe as Record<string, unknown>,
            };
          } else {
            // If scraping fails, create a basic recipe with just the URL
            recipeData.title = `Recipe from ${new URL(url).hostname}`;
          }
        } else {
          // If auto-scrape is disabled, create a basic recipe with just the URL
          recipeData.title = `Recipe from ${new URL(url).hostname}`;
        }

        // Create the recipe using the authenticated server client
        const recipe = await createRecipe(recipeData as RecipeInsert, supabase);

        results.push({
          url: url.trim(),
          success: true,
          recipe,
        });
        imported++;
      } catch (error) {
        results.push({
          url: url.trim(),
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
        failed++;
      }
    }

    const response: BulkImportResult = {
      success: imported > 0,
      imported,
      failed,
      results,
    };

    return NextResponse.json(response);
  } catch (error) {
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
