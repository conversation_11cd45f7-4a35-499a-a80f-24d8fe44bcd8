import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';

// GET /api/rss-feeds/test - Test database connection and table existence
export async function GET(request: NextRequest) {
  try {
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll() {
            // No-op for server-side
          },
        },
      }
    );

    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Test if rss_feeds table exists
    const { error: feedsError } = await supabase
      .from('rss_feeds')
      .select('count')
      .limit(1);

    // Test if rss_import_logs table exists
    const { error: logsError } = await supabase
      .from('rss_import_logs')
      .select('count')
      .limit(1);

    // Check if recipes table has RSS columns
    const { error: recipesError } = await supabase
      .from('recipes')
      .select('rss_feed_id, rss_item_guid')
      .limit(1);

    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email,
      },
      tables: {
        rss_feeds: {
          exists: !feedsError,
          error: feedsError?.message,
          code: feedsError?.code,
        },
        rss_import_logs: {
          exists: !logsError,
          error: logsError?.message,
          code: logsError?.code,
        },
        recipes_rss_columns: {
          exists: !recipesError,
          error: recipesError?.message,
          code: recipesError?.code,
        },
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error testing RSS feed setup:', error);
    return NextResponse.json(
      { error: 'Failed to test RSS feed setup' },
      { status: 500 }
    );
  }
}
