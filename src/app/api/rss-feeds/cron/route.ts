import { NextRequest, NextResponse } from 'next/server';
import { RssRecipeImporter } from '@/lib/rssImporter';
import { getActiveFeedsForImport } from '@/lib/rssFeeds';

// POST /api/rss-feeds/cron - Scheduled import endpoint for cron jobs
export async function POST(request: NextRequest) {
  try {
    // Verify the request is authorized (you can add API key authentication here)
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET;

    if (cronSecret && authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get all active feeds across all users
    const feeds = await getActiveFeedsForImport();

    if (feeds.length === 0) {
      return NextResponse.json({
        message: 'No active feeds to import',
        processed: 0,
      });
    }

    const importer = new RssRecipeImporter();
    const results = [];
    let totalImported = 0;
    let totalErrors = 0;

    // Process each feed
    for (const feed of feeds) {
      try {
        const result = await importer.importFromFeed(feed.id, feed.user_id, {
          maxItems: 10, // Limit to 10 new items per feed per run
          skipDuplicates: true,
          autoScrapeImages: true,
        });

        results.push({
          ...result,
          feedName: feed.name,
          userId: feed.user_id,
        });

        totalImported += result.importedCount;
        totalErrors += result.errors.length;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error';
        results.push({
          feedId: feed.id,
          feedName: feed.name,
          userId: feed.user_id,
          totalItems: 0,
          importedCount: 0,
          skippedCount: 0,
          errors: [errorMessage],
          importedRecipes: [],
        });
        totalErrors++;
      }
    }

    return NextResponse.json({
      message: 'Scheduled import completed',
      summary: {
        totalFeeds: feeds.length,
        totalImported,
        totalErrors,
        timestamp: new Date().toISOString(),
      },
      results,
    });
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error in scheduled RSS import:', error);

    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json(
      { error: `Scheduled import failed: ${errorMessage}` },
      { status: 500 }
    );
  }
}

// GET /api/rss-feeds/cron - Get information about the cron endpoint
export async function GET() {
  return NextResponse.json({
    message: 'RSS Feed Cron Import Endpoint',
    description: 'POST to this endpoint to trigger scheduled RSS feed imports',
    authentication: process.env.CRON_SECRET
      ? 'Bearer token required'
      : 'No authentication required',
    usage: {
      method: 'POST',
      headers: process.env.CRON_SECRET
        ? {
            Authorization: 'Bearer YOUR_CRON_SECRET',
            'Content-Type': 'application/json',
          }
        : {
            'Content-Type': 'application/json',
          },
      body: 'Empty JSON object: {}',
    },
    example_cron: '0 */6 * * * # Run every 6 hours',
  });
}
