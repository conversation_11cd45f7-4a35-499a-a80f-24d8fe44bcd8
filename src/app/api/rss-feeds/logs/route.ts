import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { getRssImportLogs, getAllRssImportLogs } from '@/lib/rssFeeds';

// GET /api/rss-feeds/logs - Get RSS import logs
export async function GET(request: NextRequest) {
  try {
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll() {
            // No-op for server-side
          },
        },
      }
    );

    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const feedId = searchParams.get('feedId');
    const limit = parseInt(searchParams.get('limit') || '50');

    if (feedId) {
      // Get logs for specific feed
      const feedIdNum = parseInt(feedId);
      if (isNaN(feedIdNum)) {
        return NextResponse.json({ error: 'Invalid feed ID' }, { status: 400 });
      }

      const logs = await getRssImportLogs(feedIdNum, limit);
      return NextResponse.json({ logs });
    } else {
      // Get all logs for user's feeds
      const logs = await getAllRssImportLogs(limit);
      return NextResponse.json({ logs });
    }
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error fetching RSS import logs:', error);
    return NextResponse.json(
      { error: 'Failed to fetch import logs' },
      { status: 500 }
    );
  }
}
