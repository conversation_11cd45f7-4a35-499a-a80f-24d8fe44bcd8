import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { validateRssFeedUrl } from '@/lib/rssFeeds';

interface RouteContext {
  params: Promise<{
    id: string;
  }>;
}

// GET /api/rss-feeds/[id] - Get a specific RSS feed
export async function GET(request: NextRequest, { params }: RouteContext) {
  try {
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll() {
            // No-op for server-side
          },
        },
      }
    );

    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const feedId = parseInt(id);
    if (isNaN(feedId)) {
      return NextResponse.json({ error: 'Invalid feed ID' }, { status: 400 });
    }

    // Get RSS feed using server-side Supabase client
    const { data: feed, error: feedError } = await supabase
      .from('rss_feeds')
      .select('*')
      .eq('id', feedId)
      .single();

    if (feedError || !feed) {
      return NextResponse.json(
        { error: 'RSS feed not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ feed });
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error fetching RSS feed:', error);
    return NextResponse.json(
      { error: 'Failed to fetch RSS feed' },
      { status: 500 }
    );
  }
}

// PUT /api/rss-feeds/[id] - Update an RSS feed
export async function PUT(request: NextRequest, { params }: RouteContext) {
  try {
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll() {
            // No-op for server-side
          },
        },
      }
    );

    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const feedId = parseInt(id);
    if (isNaN(feedId)) {
      return NextResponse.json({ error: 'Invalid feed ID' }, { status: 400 });
    }

    const body = await request.json();
    const { name, url, description, is_active } = body;

    // Validate URL if provided
    if (url && !validateRssFeedUrl(url)) {
      return NextResponse.json(
        { error: 'Invalid URL format' },
        { status: 400 }
      );
    }

    // Check if feed exists using server-side Supabase client
    const { data: existingFeed, error: feedError } = await supabase
      .from('rss_feeds')
      .select('*')
      .eq('id', feedId)
      .single();

    if (feedError || !existingFeed) {
      return NextResponse.json(
        { error: 'RSS feed not found' },
        { status: 404 }
      );
    }

    // Update the feed
    const updates: Record<string, unknown> = {};
    if (name !== undefined) updates.name = name;
    if (url !== undefined) updates.url = url;
    if (description !== undefined) updates.description = description;
    if (is_active !== undefined) updates.is_active = is_active;

    // Update RSS feed using server-side Supabase client
    const { data: feed, error: updateError } = await supabase
      .from('rss_feeds')
      .update(updates)
      .eq('id', feedId)
      .select()
      .single();

    if (updateError) {
      if (updateError.code === '23505') {
        // Unique constraint violation
        return NextResponse.json(
          { error: 'RSS feed URL already exists' },
          { status: 409 }
        );
      }
      throw new Error('Failed to update RSS feed');
    }

    return NextResponse.json({ feed });
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error updating RSS feed:', error);

    if (error instanceof Error) {
      if (error.message.includes('already exists')) {
        return NextResponse.json(
          { error: 'RSS feed URL already exists' },
          { status: 409 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Failed to update RSS feed' },
      { status: 500 }
    );
  }
}

// DELETE /api/rss-feeds/[id] - Delete an RSS feed
export async function DELETE(request: NextRequest, { params }: RouteContext) {
  try {
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll() {
            // No-op for server-side
          },
        },
      }
    );

    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const feedId = parseInt(id);
    if (isNaN(feedId)) {
      return NextResponse.json({ error: 'Invalid feed ID' }, { status: 400 });
    }

    // Check if feed exists using server-side Supabase client
    const { data: existingFeed, error: feedError } = await supabase
      .from('rss_feeds')
      .select('*')
      .eq('id', feedId)
      .single();

    if (feedError || !existingFeed) {
      return NextResponse.json(
        { error: 'RSS feed not found' },
        { status: 404 }
      );
    }

    // Delete RSS feed using server-side Supabase client
    const { error: deleteError } = await supabase
      .from('rss_feeds')
      .delete()
      .eq('id', feedId);

    if (deleteError) {
      throw new Error('Failed to delete RSS feed');
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error deleting RSS feed:', error);
    return NextResponse.json(
      { error: 'Failed to delete RSS feed' },
      { status: 500 }
    );
  }
}

// PATCH /api/rss-feeds/[id] - Toggle RSS feed status or perform other actions
export async function PATCH(request: NextRequest, { params }: RouteContext) {
  try {
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll() {
            // No-op for server-side
          },
        },
      }
    );

    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const feedId = parseInt(id);
    if (isNaN(feedId)) {
      return NextResponse.json({ error: 'Invalid feed ID' }, { status: 400 });
    }

    const body = await request.json();
    const { action, is_active } = body;

    // Check if feed exists using server-side Supabase client
    const { data: existingFeed, error: feedError } = await supabase
      .from('rss_feeds')
      .select('*')
      .eq('id', feedId)
      .single();

    if (feedError || !existingFeed) {
      return NextResponse.json(
        { error: 'RSS feed not found' },
        { status: 404 }
      );
    }

    if (action === 'toggle' || is_active !== undefined) {
      const newStatus =
        is_active !== undefined ? is_active : !existingFeed.is_active;

      // Update RSS feed status using server-side Supabase client
      const { data: feed, error: updateError } = await supabase
        .from('rss_feeds')
        .update({ is_active: newStatus })
        .eq('id', feedId)
        .select()
        .single();

      if (updateError) {
        throw new Error('Failed to update RSS feed status');
      }

      return NextResponse.json({ feed });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error updating RSS feed:', error);
    return NextResponse.json(
      { error: 'Failed to update RSS feed' },
      { status: 500 }
    );
  }
}
