import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { RssRecipeImporter } from '@/lib/rssImporter';

// POST /api/rss-feeds/import - Import recipes from RSS feeds
export async function POST(request: NextRequest) {
  try {
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll() {
            // No-op for server-side
          },
        },
      }
    );

    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      feedId,
      importAll = false,
      maxItems = 20,
      skipDuplicates = true,
      autoScrapeImages = true,
    } = body;

    const importer = new RssRecipeImporter(supabase);

    if (importAll) {
      // Import from all active feeds
      const results = await importer.importFromAllFeeds(user.id, {
        maxItems,
        skipDuplicates,
        autoScrapeImages,
      });

      const summary = {
        totalFeeds: results.length,
        totalItems: results.reduce((sum, r) => sum + r.totalItems, 0),
        totalImported: results.reduce((sum, r) => sum + r.importedCount, 0),
        totalSkipped: results.reduce((sum, r) => sum + r.skippedCount, 0),
        totalErrors: results.reduce((sum, r) => sum + r.errors.length, 0),
        results,
      };

      return NextResponse.json({ success: true, summary });
    } else {
      // Import from specific feed
      if (!feedId) {
        return NextResponse.json(
          { error: 'Feed ID is required when not importing from all feeds' },
          { status: 400 }
        );
      }

      const feedIdNum = parseInt(feedId);
      if (isNaN(feedIdNum)) {
        return NextResponse.json({ error: 'Invalid feed ID' }, { status: 400 });
      }

      // Check if feed exists and belongs to user
      const { data: feed, error: feedError } = await supabase
        .from('rss_feeds')
        .select('*')
        .eq('id', feedIdNum)
        .single();

      if (feedError || !feed) {
        return NextResponse.json(
          { error: 'RSS feed not found' },
          { status: 404 }
        );
      }

      const result = await importer.importFromFeed(feedIdNum, user.id, {
        maxItems,
        skipDuplicates,
        autoScrapeImages,
      });

      return NextResponse.json({ success: true, result });
    }
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json(
      { error: `Failed to import recipes: ${errorMessage}` },
      { status: 500 }
    );
  }
}
