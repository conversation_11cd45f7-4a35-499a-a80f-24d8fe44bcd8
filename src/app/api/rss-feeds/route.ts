import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { validateRssFeedUrl, extractDomainFromUrl } from '@/lib/rssFeeds';
import { RssRecipeImporter } from '@/lib/rssImporter';

// GET /api/rss-feeds - Get all RSS feeds for the current user
export async function GET(request: NextRequest) {
  try {
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll() {
            // No-op for server-side
          },
        },
      }
    );

    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Fetch RSS feeds directly using the server-side Supabase client
    const { data: feeds, error: fetchError } = await supabase
      .from('rss_feeds')
      .select('*')
      .order('created_at', { ascending: false });

    if (fetchError) {
      throw new Error(`Failed to fetch RSS feeds: ${fetchError.message}`);
    }
    return NextResponse.json({ feeds: feeds || [] });
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error fetching RSS feeds:', error);
    return NextResponse.json(
      { error: 'Failed to fetch RSS feeds' },
      { status: 500 }
    );
  }
}

// POST /api/rss-feeds - Create a new RSS feed
export async function POST(request: NextRequest) {
  try {
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll() {
            // No-op for server-side
          },
        },
      }
    );

    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { name, url, description, testFeed = false } = body;

    // Validate required fields
    if ((!name && !testFeed) || !url) {
      return NextResponse.json(
        { error: 'Name and URL are required' },
        { status: 400 }
      );
    }

    // Validate URL format
    if (!validateRssFeedUrl(url)) {
      return NextResponse.json(
        { error: 'Invalid URL format' },
        { status: 400 }
      );
    }

    // Test the RSS feed if requested
    if (testFeed) {
      const importer = new RssRecipeImporter(supabase);
      const testResult = await importer.testFeedUrl(url);

      if (!testResult.isValid) {
        return NextResponse.json(
          { error: `RSS feed test failed: ${testResult.error}` },
          { status: 400 }
        );
      }

      return NextResponse.json({
        valid: true,
        feedTitle: testResult.feedTitle,
        sampleItems: testResult.sampleItems,
        domain: extractDomainFromUrl(url),
      });
    }

    // Create the RSS feed directly using the server-side Supabase client
    const { data: feed, error: createError } = await supabase
      .from('rss_feeds')
      .insert({
        name,
        url,
        description: description || null,
        user_id: user.id,
      })
      .select()
      .single();

    if (createError) {
      if (createError.code === '23505') {
        // Unique constraint violation
        return NextResponse.json(
          { error: 'RSS feed URL already exists' },
          { status: 409 }
        );
      }
      // Include the actual error details for debugging
      throw new Error(
        `Failed to create RSS feed: ${createError.message} (Code: ${createError.code})`
      );
    }

    return NextResponse.json({ feed }, { status: 201 });
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error creating RSS feed:', error);

    if (error instanceof Error) {
      if (error.message.includes('already exists')) {
        return NextResponse.json(
          { error: 'RSS feed URL already exists' },
          { status: 409 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Failed to create RSS feed' },
      { status: 500 }
    );
  }
}
