import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';

// POST /api/rss-feeds/setup - Create RSS feed tables (for development/testing)
export async function POST(request: NextRequest) {
  try {
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll() {
            // No-op for server-side
          },
        },
      }
    );

    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Note: This endpoint provides SQL commands that need to be run manually in Supabase
    // We cannot create tables directly from the client due to RLS restrictions
    
    const sqlCommands = `
-- RSS Feed Tables Setup
-- Copy and paste these commands into your Supabase SQL Editor

-- Create RSS feeds table
CREATE TABLE IF NOT EXISTS rss_feeds (
  id BIGSERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  url TEXT NOT NULL UNIQUE,
  description TEXT,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  is_active BOOLEAN DEFAULT true,
  last_checked_at TIMESTAMPTZ,
  last_successful_import_at TIMESTAMPTZ,
  import_count INTEGER DEFAULT 0,
  error_count INTEGER DEFAULT 0,
  last_error TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create RSS import logs table
CREATE TABLE IF NOT EXISTS rss_import_logs (
  id BIGSERIAL PRIMARY KEY,
  rss_feed_id BIGINT NOT NULL REFERENCES rss_feeds(id) ON DELETE CASCADE,
  status TEXT NOT NULL CHECK (status IN ('success', 'error', 'partial')),
  items_found INTEGER DEFAULT 0,
  items_imported INTEGER DEFAULT 0,
  items_skipped INTEGER DEFAULT 0,
  error_message TEXT,
  started_at TIMESTAMPTZ NOT NULL,
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add RSS-related columns to recipes table
ALTER TABLE recipes 
ADD COLUMN IF NOT EXISTS rss_feed_id BIGINT REFERENCES rss_feeds(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS rss_item_guid TEXT;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_rss_feeds_user_id ON rss_feeds(user_id);
CREATE INDEX IF NOT EXISTS idx_rss_feeds_is_active ON rss_feeds(is_active);
CREATE INDEX IF NOT EXISTS idx_rss_feeds_last_checked ON rss_feeds(last_checked_at);

CREATE INDEX IF NOT EXISTS idx_rss_import_logs_feed_id ON rss_import_logs(rss_feed_id);
CREATE INDEX IF NOT EXISTS idx_rss_import_logs_status ON rss_import_logs(status);
CREATE INDEX IF NOT EXISTS idx_rss_import_logs_started_at ON rss_import_logs(started_at);

CREATE INDEX IF NOT EXISTS idx_recipes_rss_feed_id ON recipes(rss_feed_id);
CREATE INDEX IF NOT EXISTS idx_recipes_rss_item_guid ON recipes(rss_item_guid);

-- Create unique constraint to prevent duplicate RSS items
CREATE UNIQUE INDEX IF NOT EXISTS idx_recipes_rss_unique 
ON recipes(rss_feed_id, rss_item_guid) 
WHERE rss_feed_id IS NOT NULL AND rss_item_guid IS NOT NULL;

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply trigger to rss_feeds table
DROP TRIGGER IF EXISTS update_rss_feeds_updated_at ON rss_feeds;
CREATE TRIGGER update_rss_feeds_updated_at
    BEFORE UPDATE ON rss_feeds
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE rss_feeds ENABLE ROW LEVEL SECURITY;
ALTER TABLE rss_import_logs ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for rss_feeds
CREATE POLICY "Users can view their own RSS feeds" ON rss_feeds
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own RSS feeds" ON rss_feeds
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own RSS feeds" ON rss_feeds
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own RSS feeds" ON rss_feeds
    FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for rss_import_logs
CREATE POLICY "Users can view import logs for their RSS feeds" ON rss_import_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM rss_feeds 
            WHERE rss_feeds.id = rss_import_logs.rss_feed_id 
            AND rss_feeds.user_id = auth.uid()
        )
    );

CREATE POLICY "System can insert import logs" ON rss_import_logs
    FOR INSERT WITH CHECK (true);

CREATE POLICY "System can update import logs" ON rss_import_logs
    FOR UPDATE USING (true);

-- Grant necessary permissions
GRANT ALL ON rss_feeds TO authenticated;
GRANT ALL ON rss_import_logs TO authenticated;
GRANT USAGE ON SEQUENCE rss_feeds_id_seq TO authenticated;
GRANT USAGE ON SEQUENCE rss_import_logs_id_seq TO authenticated;
`;

    return NextResponse.json({
      message: 'RSS Feed Setup Instructions',
      instructions: [
        '1. Copy the SQL commands below',
        '2. Go to your Supabase Dashboard > SQL Editor',
        '3. Paste and run the SQL commands',
        '4. Test the setup using /api/rss-feeds/test',
      ],
      sql: sqlCommands,
      testEndpoint: '/api/rss-feeds/test',
    });

  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error providing RSS feed setup:', error);
    return NextResponse.json(
      { error: 'Failed to provide setup instructions' },
      { status: 500 }
    );
  }
}
