import { NextRequest, NextResponse } from 'next/server';

// <PERSON>le preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const imageUrl = searchParams.get('url');

    if (!imageUrl) {
      return NextResponse.json(
        { error: 'Image URL is required' },
        { status: 400 }
      );
    }

    return await proxyImage(imageUrl);
  } catch {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { url } = await request.json();

    if (!url) {
      return NextResponse.json({ error: 'URL is required' }, { status: 400 });
    }

    // Validate URL format
    try {
      new URL(url);
    } catch {
      return NextResponse.json(
        { error: 'Invalid URL format' },
        { status: 400 }
      );
    }

    // Fetch the webpage with comprehensive headers to avoid blocking
    const response = await fetch(url, {
      headers: {
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        Accept:
          'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        DNT: '1',
        Connection: 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Cache-Control': 'max-age=0',
      },
      // Add timeout to prevent hanging requests
      signal: AbortSignal.timeout(10000), // 10 second timeout
    });

    if (!response.ok) {
      const errorMessage =
        response.status === 403
          ? 'Access forbidden - website blocks scraping'
          : response.status === 404
            ? 'Page not found'
            : response.status === 429
              ? 'Rate limited - too many requests'
              : `Failed to fetch webpage (${response.status})`;

      return NextResponse.json(
        { error: errorMessage },
        { status: response.status }
      );
    }

    const html = await response.text();

    // Extract images from HTML
    const images = extractImagesFromHtml(html, url);

    // Return response with CORS headers
    const jsonResponse = NextResponse.json({ images });
    jsonResponse.headers.set('Access-Control-Allow-Origin', '*');
    jsonResponse.headers.set(
      'Access-Control-Allow-Methods',
      'GET, POST, OPTIONS'
    );
    jsonResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type');

    return jsonResponse;
  } catch (error) {
    // Handle specific error types
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        return NextResponse.json(
          { error: 'Request timeout - website took too long to respond' },
          { status: 408 }
        );
      }
      if (error.message.includes('fetch')) {
        return NextResponse.json(
          { error: 'Network error - unable to reach website' },
          { status: 503 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

function extractImagesFromHtml(html: string, baseUrl: string): string[] {
  const images: string[] = [];
  const imageRegex = /<img[^>]+src=["']([^"']+)["'][^>]*>/gi;

  // Also look for Open Graph images
  const ogImageRegex =
    /<meta[^>]+property=["']og:image["'][^>]+content=["']([^"']+)["'][^>]*>/gi;

  // Look for Twitter card images
  const twitterImageRegex =
    /<meta[^>]+name=["']twitter:image["'][^>]+content=["']([^"']+)["'][^>]*>/gi;

  let match;

  // Extract Open Graph images (usually high quality)
  while ((match = ogImageRegex.exec(html)) !== null) {
    const imageUrl = resolveUrl(match[1], baseUrl);
    if (imageUrl && isValidImageUrl(imageUrl)) {
      // Always return the original URL, not the proxy URL
      images.push(imageUrl);
    }
  }

  // Extract Twitter card images
  while ((match = twitterImageRegex.exec(html)) !== null) {
    const imageUrl = resolveUrl(match[1], baseUrl);
    if (imageUrl && isValidImageUrl(imageUrl)) {
      // Always return the original URL, not the proxy URL
      images.push(imageUrl);
    }
  }

  // Extract regular img tags
  while ((match = imageRegex.exec(html)) !== null) {
    const imageUrl = resolveUrl(match[1], baseUrl);
    if (imageUrl && isValidImageUrl(imageUrl)) {
      // Always return the original URL, not the proxy URL
      images.push(imageUrl);
    }
  }

  // Remove duplicates and return first 10 images
  return [...new Set(images)].slice(0, 10);
}

function resolveUrl(url: string, baseUrl: string): string | null {
  try {
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }
    if (url.startsWith('//')) {
      return `https:${url}`;
    }
    if (url.startsWith('/')) {
      const base = new URL(baseUrl);
      return `${base.protocol}//${base.host}${url}`;
    }
    return new URL(url, baseUrl).href;
  } catch {
    return null;
  }
}

function isValidImageUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname.toLowerCase();

    // Check for common image extensions
    const imageExtensions = [
      '.jpg',
      '.jpeg',
      '.png',
      '.gif',
      '.webp',
      '.svg',
      '.bmp',
    ];
    const hasImageExtension = imageExtensions.some((ext) =>
      pathname.endsWith(ext)
    );

    // Check for common image hosting domains or if it has image extension
    const imageHosts = [
      'images.unsplash.com',
      'cdn.',
      'img.',
      'image.',
      'static.',
    ];
    const isImageHost = imageHosts.some((host) =>
      urlObj.hostname.includes(host)
    );

    // Filter out obviously non-image URLs
    const excludePatterns = ['logo', 'icon', 'avatar', 'profile'];
    const isLikelyNotRecipeImage = excludePatterns.some(
      (pattern) =>
        pathname.includes(pattern) &&
        !pathname.includes('recipe') &&
        !pathname.includes('food')
    );

    return (hasImageExtension || isImageHost) && !isLikelyNotRecipeImage;
  } catch {
    return false;
  }
}

/**
 * Proxy an image from an external URL
 */
async function proxyImage(imageUrl: string): Promise<NextResponse> {
  try {
    // Validate URL format
    try {
      new URL(imageUrl);
    } catch {
      return NextResponse.json(
        { error: 'Invalid URL format' },
        { status: 400 }
      );
    }

    // Fetch the image with headers that bypass referrer restrictions
    const response = await fetch(imageUrl, {
      headers: {
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        Accept: 'image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        DNT: '1',
        Connection: 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'image',
        'Sec-Fetch-Mode': 'no-cors',
        'Sec-Fetch-Site': 'cross-site',
        'Cache-Control': 'max-age=0',
        // Important: Don't send referrer to bypass referrer restrictions
        'Referrer-Policy': 'no-referrer',
      },
      // Add timeout to prevent hanging requests
      signal: AbortSignal.timeout(15000), // 15 second timeout for images
    });

    if (!response.ok) {
      const errorMessage =
        response.status === 403
          ? 'Image access forbidden'
          : response.status === 404
            ? 'Image not found'
            : response.status === 429
              ? 'Rate limited'
              : `Failed to fetch image (${response.status})`;

      return NextResponse.json(
        { error: errorMessage },
        { status: response.status }
      );
    }

    // Get the image content type
    const contentType = response.headers.get('content-type') || 'image/jpeg';

    // Validate that it's actually an image
    if (!contentType.startsWith('image/')) {
      return NextResponse.json(
        { error: 'URL does not point to an image' },
        { status: 400 }
      );
    }

    // Get the image data
    const imageBuffer = await response.arrayBuffer();

    // Return the image with proper headers
    return new NextResponse(imageBuffer, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=86400', // Cache for 24 hours
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });
  } catch (error) {
    // Handle specific error types
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        return NextResponse.json(
          { error: 'Image request timeout' },
          { status: 408 }
        );
      }
      if (error.message.includes('fetch')) {
        return NextResponse.json(
          { error: 'Network error while fetching image' },
          { status: 503 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
