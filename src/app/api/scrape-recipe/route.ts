import { NextRequest, NextResponse } from 'next/server';
import { scrapeRecipeFromUrlServerSide } from '@/lib/recipeScraper';

// <PERSON>le preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}

export async function POST(request: NextRequest) {
  try {
    const { url } = await request.json();

    if (!url) {
      return NextResponse.json({ error: 'URL is required' }, { status: 400 });
    }

    // Use the server-side scraping function
    const scrapingResult = await scrapeRecipeFromUrlServerSide(url);

    if (scrapingResult.error) {
      // Map specific errors to appropriate HTTP status codes
      const status = scrapingResult.error.includes('Invalid URL format')
        ? 400
        : scrapingResult.error.includes('Access forbidden')
          ? 403
          : scrapingResult.error.includes('Page not found')
            ? 404
            : scrapingResult.error.includes('Request timeout')
              ? 408
              : scrapingResult.error.includes('Rate limited')
                ? 429
                : scrapingResult.error.includes('Network error')
                  ? 503
                  : 500;

      return NextResponse.json({ error: scrapingResult.error }, { status });
    }

    // Return response with CORS headers
    const jsonResponse = NextResponse.json({
      recipe: scrapingResult.recipe || {},
    });
    jsonResponse.headers.set('Access-Control-Allow-Origin', '*');
    jsonResponse.headers.set(
      'Access-Control-Allow-Methods',
      'GET, POST, OPTIONS'
    );
    jsonResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type');

    return jsonResponse;
  } catch {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
