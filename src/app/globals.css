@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #1a1a1a;
  --primary: #ff7f00;
  --secondary: #fff5f0;
  --accent: #ff5500;
  --font-primary: 'Inter', sans-serif;
  --font-heading: 'Poppins', sans-serif;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 222.2 84% 4.9%;
  --muted: 210 40% 92%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
}

.dark {
  --background: #3d1a0b;
  --foreground: #fff5f0;
  --primary: #ffa366;
  --secondary: #662b15;
  --accent: #ff6b2b;
  --font-primary: 'Inter', sans-serif;
  --font-heading: 'Poppins', sans-serif;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 212.7 26.8% 83.9%;
  --muted: 217.2 32.6% 25%;
  --muted-foreground: 215 20.2% 65.1%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
}

/* Mobile-specific improvements */
@media (max-width: 768px) {
  /* Improve touch targets for buttons */
  button:not(.btn-sm) {
    min-height: 44px;
  }

  /* Ensure small buttons still have good touch targets */
  .btn-sm, button[class*="size-sm"] {
    min-height: 40px;
    padding: 0.5rem 1rem;
  }

  /* Better text readability on mobile */
  body {
    -webkit-text-size-adjust: 100%;
    text-size-adjust: 100%;
  }

  /* Prevent horizontal scroll */
  html, body {
    overflow-x: hidden;
  }

  /* Improve form inputs on mobile */
  input, textarea, select {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  /* Better spacing for mobile cards */
  .grid {
    gap: 0.75rem;
  }
}
