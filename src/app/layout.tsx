'use client';

import { G<PERSON><PERSON>, <PERSON>eist_Mono } from 'next/font/google';
import './globals.css';
import { useEffect, useState } from 'react';
import { initializeTheme } from '@/utils/themeManager';
import Link from 'next/link';
import { AuthProvider } from '@/contexts/AuthContext';
import UserMenu from '@/components/UserMenu';
import { useDarkMode } from '@/hooks/useDarkMode';
import { Menu, X } from 'lucide-react';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const [darkMode] = useDarkMode();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    initializeTheme(darkMode);
  }, [darkMode]);

  return (
    <html lang="en" className={`h-full ${darkMode ? 'dark' : ''}`}>
      <body
        className={`${geistSans.variable} ${geistMono.variable} font-sans antialiased h-full`}
      >
        <AuthProvider>
          <div className="min-h-screen bg-background text-foreground">
            <header className="bg-secondary shadow">
              <div className="max-w-7xl mx-auto py-4 px-4 sm:py-6 sm:px-6 lg:px-8">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-4 sm:gap-8">
                    <Link href="/" className="text-2xl sm:text-3xl font-bold text-primary">
                      Recipe Picker
                    </Link>
                    <nav className="hidden md:flex items-center gap-6">
                      <Link
                        href="/"
                        className="text-foreground hover:text-primary transition-colors"
                      >
                        Recipes
                      </Link>
                      <Link
                        href="/folders"
                        className="text-foreground hover:text-primary transition-colors"
                      >
                        Manage Folders
                      </Link>
                    </nav>
                  </div>
                  <div className="flex items-center gap-2 sm:gap-4">
                    {/* Mobile menu button */}
                    <button
                      className="md:hidden p-2 text-foreground hover:text-primary transition-colors"
                      onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                      aria-label="Toggle mobile menu"
                    >
                      {isMobileMenuOpen ? (
                        <X className="h-6 w-6" />
                      ) : (
                        <Menu className="h-6 w-6" />
                      )}
                    </button>
                    <UserMenu />
                  </div>
                </div>

                {/* Mobile navigation menu */}
                {isMobileMenuOpen && (
                  <nav className="md:hidden mt-4 pt-4 border-t border-border">
                    <div className="flex flex-col space-y-3">
                      <Link
                        href="/"
                        className="text-foreground hover:text-primary transition-colors py-2"
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        Recipes
                      </Link>
                      <Link
                        href="/folders"
                        className="text-foreground hover:text-primary transition-colors py-2"
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        Manage Folders
                      </Link>
                    </div>
                  </nav>
                )}
              </div>
            </header>
            {children}
          </div>
        </AuthProvider>
      </body>
    </html>
  );
}
