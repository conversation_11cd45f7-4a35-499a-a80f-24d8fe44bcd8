'use client';

import { useState, useEffect, useMemo, useCallback, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Card from '@/components/Card';
import AddRecipeModal from '@/components/AddRecipeModal';
import ImportRecipesModal from '@/components/ImportRecipesModal';
import Pagination from '@/components/Pagination';
import RecipeFilters from '@/components/RecipeFilters';
import { getRecipes } from '@/lib/recipes';
import { RecipeWithRssFeed } from '@/lib/supabase';
import { filterRecipes } from '@/lib/tagUtils';
import { Button } from '@/components/ui';
import { Download, Star, FolderOpen } from 'lucide-react';
import Link from 'next/link';

const RECIPES_PER_PAGE = 12;

function HomeContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [recipes, setRecipes] = useState<RecipeWithRssFeed[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCuisines, setSelectedCuisines] = useState<string[]>([]);
  const [selectedIngredients, setSelectedIngredients] = useState<string[]>([]);
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);

  // Get current page from URL query params, default to 1
  const currentPage = parseInt(searchParams.get('page') || '1', 10);

  // Initialize search query and filters from URL params on component mount
  useEffect(() => {
    const urlSearchQuery = searchParams.get('search') || '';
    const urlCuisines =
      searchParams.get('cuisines')?.split(',').filter(Boolean) || [];
    const urlIngredients =
      searchParams.get('ingredients')?.split(',').filter(Boolean) || [];
    const urlFilter = searchParams.get('filter') || '';

    setSearchQuery(urlSearchQuery);
    setSelectedCuisines(urlCuisines);
    setSelectedIngredients(urlIngredients);
    setShowFavoritesOnly(urlFilter === 'favorites');
  }, [searchParams]);

  // Filter recipes based on search query, cuisine, ingredients, and favorites
  const filteredRecipes = useMemo(() => {
    let filtered = filterRecipes(
      recipes,
      searchQuery,
      selectedCuisines,
      selectedIngredients
    );

    // Apply favorites filter if enabled
    if (showFavoritesOnly) {
      filtered = filtered.filter(recipe => recipe.is_favorite);
    }

    return filtered;
  }, [recipes, searchQuery, selectedCuisines, selectedIngredients, showFavoritesOnly]);

  // Calculate pagination
  const paginationData = useMemo(() => {
    const totalRecipes = filteredRecipes.length;
    const totalPages = Math.ceil(totalRecipes / RECIPES_PER_PAGE);
    const startIndex = (currentPage - 1) * RECIPES_PER_PAGE;
    const endIndex = startIndex + RECIPES_PER_PAGE;
    const currentPageRecipes = filteredRecipes.slice(startIndex, endIndex);

    return {
      totalPages,
      currentPageRecipes,
      totalRecipes,
    };
  }, [filteredRecipes, currentPage]);

  const updateUrlParams = useCallback(
    (updates: {
      page?: number;
      search?: string;
      cuisines?: string[];
      ingredients?: string[];
    }) => {
      const params = new URLSearchParams(searchParams.toString());

      // Update page parameter
      if (updates.page !== undefined) {
        if (updates.page === 1) {
          params.delete('page');
        } else {
          params.set('page', updates.page.toString());
        }
      }

      // Update search parameter
      if (updates.search !== undefined) {
        if (updates.search.trim() === '') {
          params.delete('search');
        } else {
          params.set('search', updates.search);
        }
      }

      // Update cuisine filters
      if (updates.cuisines !== undefined) {
        if (updates.cuisines.length === 0) {
          params.delete('cuisines');
        } else {
          params.set('cuisines', updates.cuisines.join(','));
        }
      }

      // Update ingredient filters
      if (updates.ingredients !== undefined) {
        if (updates.ingredients.length === 0) {
          params.delete('ingredients');
        } else {
          params.set('ingredients', updates.ingredients.join(','));
        }
      }

      const newUrl = params.toString() ? `?${params.toString()}` : '/';
      router.push(newUrl);
    },
    [router, searchParams]
  );

  const updatePageInUrl = useCallback(
    (page: number) => {
      updateUrlParams({ page });
    },
    [updateUrlParams]
  );

  // Handle search query changes
  const handleSearchChange = useCallback(
    (newSearchQuery: string) => {
      setSearchQuery(newSearchQuery);
      // Update URL with new search query and reset to page 1
      updateUrlParams({ search: newSearchQuery, page: 1 });
    },
    [updateUrlParams]
  );

  // Handle cuisine filter toggle
  const handleCuisineToggle = useCallback(
    (cuisine: string) => {
      const newSelectedCuisines = selectedCuisines.includes(cuisine)
        ? selectedCuisines.filter((c) => c !== cuisine)
        : [...selectedCuisines, cuisine];

      setSelectedCuisines(newSelectedCuisines);
      updateUrlParams({ cuisines: newSelectedCuisines, page: 1 });
    },
    [selectedCuisines, updateUrlParams]
  );

  // Handle ingredient filter toggle
  const handleIngredientToggle = useCallback(
    (ingredient: string) => {
      const newSelectedIngredients = selectedIngredients.includes(ingredient)
        ? selectedIngredients.filter((i) => i !== ingredient)
        : [...selectedIngredients, ingredient];

      setSelectedIngredients(newSelectedIngredients);
      updateUrlParams({ ingredients: newSelectedIngredients, page: 1 });
    },
    [selectedIngredients, updateUrlParams]
  );

  // Handle clear all filters
  const handleClearFilters = useCallback(() => {
    setSelectedCuisines([]);
    setSelectedIngredients([]);
    updateUrlParams({ cuisines: [], ingredients: [], page: 1 });
  }, [updateUrlParams]);
  const handlePageChange = (page: number) => {
    updatePageInUrl(page);
  };

  const fetchRecipes = async () => {
    try {
      setIsLoading(true);
      const fetchedRecipes = await getRecipes();
      setRecipes(fetchedRecipes);
    } catch {
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchRecipes();
  }, []);

  const handleRecipeAdded = () => {
    fetchRecipes(); // Refresh the recipes list
  };

  const handleRecipeDeleted = (deletedId: number) => {
    setRecipes((prevRecipes) =>
      prevRecipes.filter((recipe) => recipe.id !== deletedId)
    );
  };

  const handleRecipeUpdated = (updatedRecipe: RecipeWithRssFeed) => {
    setRecipes((prevRecipes) =>
      prevRecipes.map((recipe) =>
        recipe.id === updatedRecipe.id ? updatedRecipe : recipe
      )
    );
  };

  // Import handlers
  const handleImportClick = () => {
    setIsImportModalOpen(true);
  };

  const handleImportClose = () => {
    setIsImportModalOpen(false);
  };

  if (isLoading) {
    return (
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="text-center">Loading recipes...</div>
        </div>
      </main>
    );
  }

  return (
    <main className="max-w-7xl mx-auto py-4 px-4 sm:py-6 sm:px-6 lg:px-8">
      <div className="py-4 sm:py-6">
        <div className="flex flex-col gap-4 mb-6">
          <div>
            <h2 className="text-xl sm:text-2xl font-bold text-primary">
              {showFavoritesOnly ? 'Your Favorite Recipes' : 'Your Recipes'}
            </h2>
            {showFavoritesOnly && (
              <p className="text-sm text-foreground/60 mt-1">
                Showing only your starred recipes
              </p>
            )}
          </div>
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
            <Button
              onClick={() => setShowFavoritesOnly(!showFavoritesOnly)}
              variant={showFavoritesOnly ? "default" : "outline"}
              className="flex items-center justify-center gap-2 w-full sm:w-auto"
              size="sm"
            >
              <Star className={`h-4 w-4 ${showFavoritesOnly ? 'fill-current' : ''}`} />
              <span className="sm:inline">{showFavoritesOnly ? 'Show All' : 'Favorites Only'}</span>
            </Button>
            <Link href="/folders" className="w-full sm:w-auto">
              <Button variant="outline" className="flex items-center justify-center gap-2 w-full" size="sm">
                <FolderOpen className="h-4 w-4" />
                <span className="sm:inline">Manage Folders</span>
              </Button>
            </Link>
            <Button
              onClick={handleImportClick}
              variant="outline"
              className="flex items-center justify-center gap-2 w-full sm:w-auto"
              size="sm"
            >
              <Download className="h-4 w-4" />
              <span className="sm:inline">Import Recipes</span>
            </Button>
            <div className="w-full sm:w-auto">
              <AddRecipeModal onRecipeAdded={handleRecipeAdded} />
            </div>
          </div>
        </div>

        {/* Search and Filter Component */}
        <div className="mb-6">
          <RecipeFilters
            recipes={recipes}
            searchQuery={searchQuery}
            selectedCuisines={selectedCuisines}
            selectedIngredients={selectedIngredients}
            onSearchChange={handleSearchChange}
            onCuisineToggle={handleCuisineToggle}
            onIngredientToggle={handleIngredientToggle}
            onClearFilters={handleClearFilters}
          />
        </div>

        {recipes.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-foreground/60 mb-4">
              No recipes yet. Add your first recipe!
            </p>
            <div className="flex justify-center">
              <AddRecipeModal onRecipeAdded={handleRecipeAdded} />
            </div>
          </div>
        ) : filteredRecipes.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-foreground/60 mb-4">
              No recipes found matching your filters.
            </p>
            <p className="text-foreground/40 text-sm">
              Try adjusting your search terms, filters, or add a new recipe.
            </p>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4">
              {paginationData.currentPageRecipes.map((recipe) => (
                <Card
                  key={recipe.id}
                  recipe={recipe}
                  onDelete={handleRecipeDeleted}
                  onUpdate={handleRecipeUpdated}
                />
              ))}
            </div>

            {paginationData.totalPages > 1 && (
              <div className="mt-8">
                <Pagination
                  currentPage={currentPage}
                  totalPages={paginationData.totalPages}
                  onPageChange={handlePageChange}
                />
              </div>
            )}
          </>
        )}

        {/* Import Recipes Modal */}
        <ImportRecipesModal
          isOpen={isImportModalOpen}
          onClose={handleImportClose}
          onImportComplete={() => {}}
          onRefresh={() => fetchRecipes()}
        />
      </div>
    </main>
  );
}

export default function Home() {
  return (
    <Suspense
      fallback={
        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            <div className="text-center">Loading...</div>
          </div>
        </main>
      }
    >
      <HomeContent />
    </Suspense>
  );
}
