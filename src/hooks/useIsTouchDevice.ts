'use client';

import { useState, useEffect } from 'react';

export function useIsTouchDevice() {
  const [isTouchDevice, setIsTouchDevice] = useState(false);

  useEffect(() => {
    const checkTouchDevice = () => {
      // Check for touch capability
      const hasTouch = 'ontouchstart' in window ||
                      navigator.maxTouchPoints > 0 ||
                      ('msMaxTouchPoints' in navigator && (navigator as Navigator & { msMaxTouchPoints: number }).msMaxTouchPoints > 0);

      // Also check screen size as a secondary indicator
      const isSmallScreen = window.innerWidth < 768; // md breakpoint

      setIsTouchDevice(hasTouch || isSmallScreen);
    };

    checkTouchDevice();
    
    // Listen for resize events to handle device orientation changes
    window.addEventListener('resize', checkTouchDevice);
    
    return () => {
      window.removeEventListener('resize', checkTouchDevice);
    };
  }, []);

  return isTouchDevice;
}
