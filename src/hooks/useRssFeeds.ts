import { useState, useEffect, useCallback } from 'react';
import { RssFeed, RssImportLog, RssFeedTestResult } from '@/lib/supabase';

export interface RssFeedFormData {
  name: string;
  url: string;
  description: string;
}

export interface ImportResult {
  feedId: number;
  totalItems: number;
  importedCount: number;
  skippedCount: number;
  errors: string[];
}

export interface ImportSummary {
  totalFeeds: number;
  totalItems: number;
  totalImported: number;
  totalSkipped: number;
  totalErrors: number;
  results: ImportResult[];
}

export interface UseRssFeedsReturn {
  feeds: RssFeed[];
  logs: (RssImportLog & { feed_name: string })[];
  isLoading: boolean;
  isImporting: boolean;
  error: string | null;
  fetchFeeds: () => Promise<void>;
  fetchLogs: (feedId?: number) => Promise<void>;
  createFeed: (data: RssFeedFormData) => Promise<RssFeed>;
  updateFeed: (id: number, data: Partial<RssFeedFormData>) => Promise<RssFeed>;
  deleteFeed: (id: number) => Promise<void>;
  toggleFeedStatus: (id: number, isActive: boolean) => Promise<RssFeed>;
  testFeedUrl: (url: string) => Promise<RssFeedTestResult>;
  importFromFeed: (
    feedId: number,
    options?: ImportOptions
  ) => Promise<ImportResult>;
  importFromAllFeeds: (options?: ImportOptions) => Promise<ImportSummary>;
}

export interface ImportOptions {
  maxItems?: number;
  skipDuplicates?: boolean;
  autoScrapeImages?: boolean;
}

export const useRssFeeds = (): UseRssFeedsReturn => {
  const [feeds, setFeeds] = useState<RssFeed[]>([]);
  const [logs, setLogs] = useState<(RssImportLog & { feed_name: string })[]>(
    []
  );
  const [isLoading, setIsLoading] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchFeeds = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/rss-feeds');
      if (!response.ok) {
        throw new Error('Failed to fetch RSS feeds');
      }
      const data = await response.json();
      setFeeds(data.feeds);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const fetchLogs = useCallback(async (feedId?: number) => {
    setIsLoading(true);
    setError(null);
    try {
      const url = feedId
        ? `/api/rss-feeds/logs?feedId=${feedId}`
        : '/api/rss-feeds/logs';
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('Failed to fetch import logs');
      }
      const data = await response.json();
      setLogs(data.logs);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const createFeed = useCallback(
    async (data: RssFeedFormData): Promise<RssFeed> => {
      setError(null);
      const response = await fetch('/api/rss-feeds', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create RSS feed');
      }

      const result = await response.json();
      setFeeds((prev) => [result.feed, ...prev]);
      return result.feed;
    },
    []
  );

  const updateFeed = useCallback(
    async (id: number, data: Partial<RssFeedFormData>): Promise<RssFeed> => {
      setError(null);
      const response = await fetch(`/api/rss-feeds/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update RSS feed');
      }

      const result = await response.json();
      setFeeds((prev) =>
        prev.map((feed) => (feed.id === id ? result.feed : feed))
      );
      return result.feed;
    },
    []
  );

  const deleteFeed = useCallback(async (id: number): Promise<void> => {
    setError(null);
    const response = await fetch(`/api/rss-feeds/${id}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to delete RSS feed');
    }

    setFeeds((prev) => prev.filter((feed) => feed.id !== id));
  }, []);

  const toggleFeedStatus = useCallback(
    async (id: number, isActive: boolean): Promise<RssFeed> => {
      setError(null);
      const response = await fetch(`/api/rss-feeds/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ is_active: isActive }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to toggle RSS feed status');
      }

      const result = await response.json();
      setFeeds((prev) =>
        prev.map((feed) => (feed.id === id ? result.feed : feed))
      );
      return result.feed;
    },
    []
  );

  const testFeedUrl = useCallback(async (url: string) => {
    setError(null);
    const response = await fetch('/api/rss-feeds', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ url, testFeed: true }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to test RSS feed');
    }

    return await response.json();
  }, []);

  const importFromFeed = useCallback(
    async (
      feedId: number,
      options: ImportOptions = {}
    ): Promise<ImportResult> => {
      setIsImporting(true);
      setError(null);
      try {
        const response = await fetch('/api/rss-feeds/import', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ feedId, ...options }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to import recipes');
        }

        const data = await response.json();

        // Refresh feeds to update statistics
        await fetchFeeds();

        return data.result;
      } finally {
        setIsImporting(false);
      }
    },
    [fetchFeeds]
  );

  const importFromAllFeeds = useCallback(
    async (options: ImportOptions = {}): Promise<ImportSummary> => {
      setIsImporting(true);
      setError(null);
      try {
        const response = await fetch('/api/rss-feeds/import', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ importAll: true, ...options }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to import recipes');
        }

        const data = await response.json();

        // Refresh feeds to update statistics
        await fetchFeeds();

        return data.summary;
      } finally {
        setIsImporting(false);
      }
    },
    [fetchFeeds]
  );

  // Load feeds on mount
  useEffect(() => {
    fetchFeeds();
  }, [fetchFeeds]);

  return {
    feeds,
    logs,
    isLoading,
    isImporting,
    error,
    fetchFeeds,
    fetchLogs,
    createFeed,
    updateFeed,
    deleteFeed,
    toggleFeedStatus,
    testFeedUrl,
    importFromFeed,
    importFromAllFeeds,
  };
};
