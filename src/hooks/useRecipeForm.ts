import { useState, useCallback } from 'react';
import { scrapeRecipeFromUrl } from '@/lib/recipeScraper';

export type FormFields = 'title' | 'description' | 'image_url' | 'recipe_url' | 'cuisine_type';

export interface RecipeFormData {
  title: string;
  description: string;
  image_url: string;
  recipe_url: string;
  cuisine_type: string;
}

export interface RecipeFormErrors {
  title?: string;
  description?: string;
  image_url?: string;
  recipe_url?: string;
  cuisine_type?: string;
}

export interface UseRecipeFormProps {
  initialData?: Partial<RecipeFormData>;
  onSubmit: (data: RecipeFormData) => Promise<void>;
  onCancel?: () => void;
}

export interface UseRecipeFormReturn {
  formData: RecipeFormData;
  errors: RecipeFormErrors;
  isLoading: boolean;
  isScrapingLoading: boolean;
  setIsLoading: (loading: boolean) => void;
  handleInputChange: (field: FormFields, value: string) => void;
  handleSubmit: (e: React.FormEvent) => Promise<void>;
  handleCancel: () => void;
  resetForm: (data?: Partial<RecipeFormData>) => void;
  validateForm: () => boolean;
  handleScrapeRecipe: () => Promise<void>;
}

const defaultFormData: RecipeFormData = {
  title: '',
  description: '',
  image_url: '',
  recipe_url: '',
  cuisine_type: '',
};

export const useRecipeForm = ({
  initialData = {},
  onSubmit,
  onCancel,
}: UseRecipeFormProps): UseRecipeFormReturn => {
  const [formData, setFormData] = useState<RecipeFormData>({
    ...defaultFormData,
    ...initialData,
  });
  const [errors, setErrors] = useState<RecipeFormErrors>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isScrapingLoading, setIsScrapingLoading] = useState(false);

  const isValidUrl = (string: string): boolean => {
    try {
      new URL(string);
      return true;
    } catch {
      return false;
    }
  };

  const validateForm = useCallback((): boolean => {
    const newErrors: RecipeFormErrors = {};

    // Only recipe_url is required now
    if (!formData.recipe_url?.trim()) {
      newErrors.recipe_url = 'Recipe URL is required';
    } else if (!isValidUrl(formData.recipe_url)) {
      newErrors.recipe_url = 'Please enter a valid URL';
    }

    // Title is optional but if provided, should not be empty
    if (formData.title && !formData.title.trim()) {
      newErrors.title = 'Title cannot be empty if provided';
    }

    if (formData.image_url && !isValidUrl(formData.image_url)) {
      newErrors.image_url = 'Please enter a valid URL';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  const handleInputChange = useCallback((field: FormFields, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Clear error when user starts typing
    setErrors((prev) => {
      if (prev[field]) {
        return {
          ...prev,
          [field]: undefined,
        };
      }
      return prev;
    });
  }, []);

  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();

      if (!validateForm()) {
        return;
      }

      setIsLoading(true);

      try {
        await onSubmit(formData);
      } catch (error) {
        // Error handling is delegated to the parent component
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [formData, onSubmit, validateForm]
  );

  const resetForm = useCallback((data: Partial<RecipeFormData> = {}) => {
    setFormData({
      ...defaultFormData,
      ...data,
    });
    setErrors({});
  }, []);

  const handleCancel = useCallback(() => {
    resetForm(initialData);
    onCancel?.();
  }, [initialData, onCancel, resetForm]);

  const handleScrapeRecipe = useCallback(async () => {
    if (!formData.recipe_url?.trim()) {
      setErrors((prev) => ({
        ...prev,
        recipe_url: 'Please enter a recipe URL first',
      }));
      return;
    }

    if (!isValidUrl(formData.recipe_url)) {
      setErrors((prev) => ({
        ...prev,
        recipe_url: 'Please enter a valid URL',
      }));
      return;
    }

    setIsScrapingLoading(true);
    try {
      const result = await scrapeRecipeFromUrl(formData.recipe_url);

      if (result.error) {
        setErrors((prev) => ({
          ...prev,
          recipe_url: `Scraping failed: ${result.error}`,
        }));
        return;
      }

      if (result.recipe) {
        // Update form data with scraped information
        setFormData((prev) => ({
          ...prev,
          title: result.recipe?.title || prev.title,
          description: result.recipe?.description || prev.description,
          image_url: result.recipe?.image || prev.image_url,
          cuisine_type: result.recipe?.cuisine || prev.cuisine_type,
        }));

        // Clear any existing errors
        setErrors({});
      }
    } catch {
      setErrors((prev) => ({
        ...prev,
        recipe_url: 'Failed to scrape recipe data',
      }));
    } finally {
      setIsScrapingLoading(false);
    }
  }, [formData.recipe_url]);

  return {
    formData,
    errors,
    isLoading,
    isScrapingLoading,
    setIsLoading,
    handleInputChange,
    handleSubmit,
    handleCancel,
    resetForm,
    validateForm,
    handleScrapeRecipe,
  };
};
