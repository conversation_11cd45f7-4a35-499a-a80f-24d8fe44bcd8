export type ThemeName = 'desert' | 'ocean' | 'forest' | 'pastel';

interface ThemeColors {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  text: string;
  error: string;
  success: string;
  surface: string;
}

interface Theme {
  light: ThemeColors;
  dark: ThemeColors;
}

const themes: Record<ThemeName, Theme> = {
  desert: {
    light: {
      primary: '#ff7f00',
      secondary: '#fff5f0',
      accent: '#ff5500',
      background: '#ffffff',
      text: '#1a1a1a',
      error: '#dc2626',
      success: '#16a34a',
      surface: '#ffffff',
    },
    dark: {
      primary: '#ffa366',
      secondary: '#662b15',
      accent: '#ff6b2b',
      background: '#3d1a0b',
      text: '#fff5f0',
      error: '#ef4444',
      success: '#22c55e',
      surface: '#4a2010',
    },
  },
  ocean: {
    light: {
      primary: '#2A9D8F',
      secondary: '#264653',
      accent: '#E9C46A',
      background: '#F8F9FA',
      text: '#2B2D42',
      error: '#E76F51',
      success: '#43AA8B',
      surface: '#FFFFFF',
    },
    dark: {
      primary: '#4ECDC4',
      secondary: '#1A535C',
      accent: '#FFD93D',
      background: '#1A1A1D',
      text: '#F8F9FA',
      error: '#FF6B6B',
      success: '#6BCB77',
      surface: '#2A2A2D',
    },
  },
  forest: {
    light: {
      primary: '#40916C',
      secondary: '#1B4332',
      accent: '#B7E4C7',
      background: '#F8FAF8',
      text: '#081C15',
      error: '#D62828',
      success: '#74C69D',
      surface: '#FFFFFF',
    },
    dark: {
      primary: '#52B788',
      secondary: '#2D6A4F',
      accent: '#95D5B2',
      background: '#1A1A1D',
      text: '#D8F3DC',
      error: '#E63946',
      success: '#74C69D',
      surface: '#2A2A2D',
    },
  },
  pastel: {
    light: {
      primary: '#6246EA',
      secondary: '#D1D1E9',
      accent: '#E45858',
      background: '#FFFFFE',
      text: '#2B2C34',
      error: '#E45858',
      success: '#6246EA',
      surface: '#F8F8F8',
    },
    dark: {
      primary: '#7B5FF2',
      secondary: '#B5B5D9',
      accent: '#E45858',
      background: '#1A1A1D',
      text: '#FFFFFE',
      error: '#E45858',
      success: '#7B5FF2',
      surface: '#2A2A2D',
    },
  },
};

export const setTheme = (themeName: ThemeName, isDark: boolean = false) => {
  if (typeof window === 'undefined') {
    return; // Don't run on server side
  }

  const root = document.documentElement;
  const theme = themes[themeName][isDark ? 'dark' : 'light'];

  Object.entries(theme).forEach(([property, value]) => {
    root.style.setProperty(`--${property}`, value);
  });

  // Save the theme preference
  localStorage.setItem('theme-preference', themeName);
};

export const getStoredTheme = (): ThemeName | null => {
  if (typeof window === 'undefined') {
    return null; // Return null on server side
  }
  return localStorage.getItem('theme-preference') as ThemeName | null;
};

export const initializeTheme = (isDark: boolean = false) => {
  const storedTheme = getStoredTheme();
  if (storedTheme && themes[storedTheme]) {
    setTheme(storedTheme, isDark);
  } else {
    // Default to 'desert' theme
    setTheme('desert', isDark);
  }
};

export const getAllThemes = (): ThemeName[] => {
  return Object.keys(themes) as ThemeName[];
};

export { themes };
