# RSS Feed Integration

This document explains how to set up and use the RSS feed integration to automatically import recipes from food blogs and recipe websites.

## Features

- **RSS Feed Management**: Add, edit, and manage RSS feed sources
- **Automatic Recipe Import**: Parse RSS feeds and extract recipe data
- **Image Scraping**: Automatically scrape recipe images from linked pages
- **Duplicate Detection**: Prevent importing the same recipe multiple times
- **Scheduled Imports**: Manual and automated import scheduling
- **Import History**: Track import logs and statistics

## Database Setup

1. **Run the SQL migration** in your Supabase SQL editor:

   ```sql
   -- Copy and paste the contents of scripts/create-rss-tables.sql
   ```

2. **Verify tables were created**:
   - `rss_feeds` - Stores RSS feed sources
   - `rss_import_logs` - Tracks import history
   - `recipes` table updated with RSS-related columns

## Usage

### Adding RSS Feeds

1. Navigate to **RSS Feeds** from the user menu
2. Click **"Add Feed"**
3. Enter the RSS feed URL (e.g., `https://example.com/feed.xml`)
4. Test the feed to verify it works
5. Provide a name and optional description
6. Save the feed

### Managing Feeds

- **Enable/Disable**: Toggle feeds on/off
- **Edit**: Update feed name, URL, or description
- **Delete**: Remove feeds (also deletes imported recipes)
- **Import**: Manually trigger imports for specific feeds

### Importing Recipes

#### Manual Import

- **Single Feed**: Click "Import" on any feed
- **All Feeds**: Click "Import All" button
- **Scheduled**: Use the "Import Now" button in Scheduled Imports

#### Automatic Import

Set up a cron job to call the import endpoint:

```bash
# Example cron job (every 6 hours)
0 */6 * * * curl -X POST https://your-domain.com/api/rss-feeds/cron \
  -H "Authorization: Bearer YOUR_CRON_SECRET" \
  -H "Content-Type: application/json" \
  -d '{}'
```

## Configuration

### Environment Variables

Add to your `.env.local`:

```env
# Optional: Secure the cron endpoint
CRON_SECRET=your-secret-key-here
```

### Import Settings

Default import settings:

- **Max items per import**: 20 recipes
- **Skip duplicates**: Enabled
- **Auto-scrape images**: Enabled
- **Recommended frequency**: Every 6 hours

## Supported RSS Feeds

The system works with standard RSS 2.0 and Atom feeds. Tested with:

- **Food blogs** (WordPress, Ghost, etc.)
- **Recipe websites** (Epicurious, The Kitchn, etc.)
- **Custom RSS feeds** with recipe content

### RSS Feed Requirements

- Valid RSS 2.0 or Atom format
- Accessible via HTTP/HTTPS
- Contains recipe-related content

## API Endpoints

### RSS Feed Management

- `GET /api/rss-feeds` - List user's feeds
- `POST /api/rss-feeds` - Create new feed
- `GET /api/rss-feeds/[id]` - Get specific feed
- `PUT /api/rss-feeds/[id]` - Update feed
- `DELETE /api/rss-feeds/[id]` - Delete feed
- `PATCH /api/rss-feeds/[id]` - Toggle feed status

### Import Operations

- `POST /api/rss-feeds/import` - Manual import
- `POST /api/rss-feeds/cron` - Scheduled import (for cron jobs)
- `GET /api/rss-feeds/logs` - Get import logs

## Troubleshooting

### Common Issues

1. **Feed not importing recipes**

   - Check if the RSS feed contains recipe content
   - Verify the feed URL is accessible
   - Check import logs for error messages

2. **Images not loading**

   - Some websites block image scraping
   - Images may be behind authentication
   - Check if the original recipe URL is accessible

3. **Duplicate recipes**
   - The system uses URL and GUID for deduplication
   - If recipes are still duplicated, check the RSS feed structure

### Import Logs

View detailed import information:

- Items found vs. imported
- Error messages
- Processing time
- Feed statistics

## Best Practices

1. **Feed Selection**

   - Choose feeds with consistent recipe content
   - Avoid feeds with mixed content (news, reviews, etc.)
   - Test feeds before adding them

2. **Import Frequency**

   - Don't import too frequently (respect website resources)
   - 6-hour intervals are recommended
   - Monitor import logs for errors

3. **Content Quality**
   - Review imported recipes for accuracy
   - Some feeds may have incomplete recipe data
   - Manual editing may be needed for some recipes

## Security Considerations

- RSS feeds are parsed server-side for security
- Image scraping respects robots.txt
- Rate limiting prevents abuse
- User data is isolated (RLS policies)

## Future Enhancements

Planned features:

- Advanced scheduling options
- Feed health monitoring
- Recipe content enhancement
- Bulk import operations
- Feed discovery suggestions
