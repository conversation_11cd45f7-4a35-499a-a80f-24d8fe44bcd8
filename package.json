{"name": "recipe-picker", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,scss,md}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,css,scss,md}\"", "clean": "rm -rf .next node_modules/.cache", "clean:all": "rm -rf .next node_modules/.cache node_modules package-lock.json && npm install", "seed": "DOTENV_CONFIG_PATH=.env.local tsx -r dotenv/config scripts/seed-recipes.ts", "seed:100": "DOTENV_CONFIG_PATH=.env.local tsx -r dotenv/config scripts/seed-100-recipes.ts"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-slot": "^1.1.1", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.8", "@tailwindcss/line-clamp": "^0.4.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.471.0", "next": "15.1.4", "react": "^18.2.0", "react-dom": "^18.2.0", "rss-parser": "^3.13.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "babel-jest": "^29.7.0", "dotenv": "^16.5.0", "eslint": "^9", "eslint-config-next": "15.1.4", "identity-obj-proxy": "^3.0.0", "jest": "^29.0.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "prettier": "^3.4.2", "tailwindcss": "^3.4.1", "tsx": "^4.19.4", "typescript": "^5"}}