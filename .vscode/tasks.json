{"version": "2.0.0", "tasks": [{"label": "dev", "type": "shell", "command": "npm", "args": ["run", "dev"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Start the Next.js development server with Turbopack"}, {"label": "build", "type": "shell", "command": "npm", "args": ["run", "build"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$tsc"], "detail": "Build the Next.js application for production"}, {"label": "start", "type": "shell", "command": "npm", "args": ["start"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Start the production Next.js server"}, {"label": "test", "type": "shell", "command": "npm", "args": ["test"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Run Jest tests"}, {"label": "lint", "type": "shell", "command": "npm", "args": ["run", "lint"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$eslint-stylish"], "detail": "Run ESLint to check for code issues"}, {"label": "format", "type": "shell", "command": "npm", "args": ["run", "format"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Format code with <PERSON><PERSON>er"}, {"label": "seed", "type": "shell", "command": "npm", "args": ["run", "seed"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Seed the database with sample recipes"}, {"label": "seed:100", "type": "shell", "command": "npm", "args": ["run", "seed:100"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Seed the database with 100 sample recipes"}]}