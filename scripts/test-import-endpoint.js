// Simple test script to verify the RSS import endpoint is accessible
// Run with: node scripts/test-import-endpoint.js

const testImportEndpoint = async () => {
  try {
    console.log('Testing RSS import endpoint...');
    
    // Test with a simple request (should return 401 since we're not authenticated)
    const response = await fetch('http://localhost:3000/api/rss-feeds/import', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        importAll: true
      })
    });

    console.log(`Response status: ${response.status}`);
    console.log(`Response status text: ${response.statusText}`);
    
    if (response.status === 404) {
      console.error('❌ 404 Error: Import endpoint not found');
      console.log('This suggests the route file is not being loaded correctly.');
    } else if (response.status === 401) {
      console.log('✅ 401 Unauthorized: Endpoint exists but requires authentication (expected)');
    } else {
      console.log(`ℹ️  Unexpected status: ${response.status}`);
    }

    const responseText = await response.text();
    console.log('Response body:', responseText);

  } catch (error) {
    console.error('❌ Error testing endpoint:', error.message);
  }
};

testImportEndpoint();
