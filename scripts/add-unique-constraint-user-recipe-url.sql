-- Add unique constraint for user_id and recipe_url combination
-- This prevents users from adding the same recipe URL multiple times
-- Run this in your Supabase SQL Editor

-- Step 1: Remove duplicate recipes, keeping the most recent one
-- This query deletes older duplicates based on updated_at timestamp
WITH duplicates AS (
  SELECT id,
         ROW_NUMBER() OVER (
           PARTITION BY user_id, recipe_url
           ORDER BY updated_at DESC, created_at DESC, id DESC
         ) as rn
  FROM recipes
  WHERE recipe_url IS NOT NULL
)
DELETE FROM recipes
WHERE id IN (
  SELECT id FROM duplicates WHERE rn > 1
);

-- Step 2: Create unique constraint to prevent duplicate recipe URLs per user
-- Note: This constraint only applies when recipe_url is NOT NULL
-- Multiple recipes with NULL recipe_url are allowed per user
CREATE UNIQUE INDEX IF NOT EXISTS idx_recipes_user_recipe_url_unique
ON recipes(user_id, recipe_url)
WHERE recipe_url IS NOT NULL;

-- Optional: Add a comment to document the constraint
COMMENT ON INDEX idx_recipes_user_recipe_url_unique IS
'Ensures each user can only have one recipe with the same recipe_url (when recipe_url is not null)';
