#!/usr/bin/env tsx
/* eslint-disable no-console */

import { createRecipe } from '../src/lib/recipes';
import { RecipeInsert } from '../src/lib/supabase';

const exampleRecipes: RecipeInsert[] = [
  {
    title: 'Classic Spaghetti Carbonara',
    description:
      'A traditional Italian pasta dish with eggs, cheese, pancetta, and black pepper. Simple ingredients come together to create a creamy, luxurious sauce without cream.',
    image_url:
      'https://images.unsplash.com/photo-1621996346565-e3dbc353d2e5?w=800&h=600&fit=crop',
    recipe_url: 'https://www.bonappetit.com/recipe/simple-carbonara',
    user_id: '',
  },
  {
    title: 'Thai Green Curry',
    description:
      'Aromatic and spicy Thai curry with coconut milk, green chilies, basil, and your choice of protein. A perfect balance of heat, sweetness, and herbs.',
    image_url:
      'https://images.unsplash.com/photo-1455619452474-d2be8b1e70cd?w=800&h=600&fit=crop',
    recipe_url: 'https://www.recipetineats.com/thai-green-curry/',
    user_id: '',
  },
  {
    title: 'Homemade Pizza Margherita',
    description:
      'Classic Neapolitan pizza with fresh mozzarella, tomato sauce, and basil. The perfect combination of crispy crust and melted cheese.',
    image_url:
      'https://images.unsplash.com/photo-1513104890138-7c749659a591?w=800&h=600&fit=crop',
    recipe_url:
      'https://www.kingarthurbaking.com/recipes/pizza-margherita-recipe',
    user_id: '',
  },
  {
    title: 'Beef Tacos with Cilantro Lime Crema',
    description:
      'Seasoned ground beef tacos topped with fresh cilantro lime crema, diced onions, and cheese. A crowd-pleasing Mexican favorite.',
    image_url:
      'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800&h=600&fit=crop',
    recipe_url: 'https://www.foodnetwork.com/recipes/beef-tacos-recipe',
    user_id: '',
  },
  {
    title: 'Chicken Tikka Masala',
    description:
      'Tender chunks of roasted marinated chicken in a spiced curry sauce. Rich, creamy, and full of aromatic Indian spices.',
    image_url:
      'https://images.unsplash.com/photo-1565557623262-b51c2513a641?w=800&h=600&fit=crop',
    recipe_url: 'https://cafedelites.com/chicken-tikka-masala/',
    user_id: '',
  },
  {
    title: 'Caesar Salad',
    description:
      'Crisp romaine lettuce with homemade Caesar dressing, parmesan cheese, and crunchy croutons. A timeless salad classic.',
    image_url:
      'https://images.unsplash.com/photo-1546793665-c74683f339c1?w=800&h=600&fit=crop',
    recipe_url:
      'https://www.foodnetwork.com/recipes/emeril-lagasse/caesar-salad-recipe-1916154',
    user_id: '',
  },
  {
    title: 'Chocolate Chip Cookies',
    description:
      'Soft and chewy chocolate chip cookies with the perfect balance of crispy edges and gooey centers. A beloved American classic.',
    image_url:
      'https://images.unsplash.com/photo-1499636136210-6f4ee915583e?w=800&h=600&fit=crop',
    recipe_url:
      'https://www.allrecipes.com/recipe/10813/best-chocolate-chip-cookies/',
    user_id: '',
  },
  {
    title: 'Beef Stir Fry',
    description:
      'Quick and easy beef stir fry with colorful vegetables in a savory sauce. Perfect for busy weeknight dinners.',
    image_url:
      'https://images.unsplash.com/photo-1603133872878-684f208fb84b?w=800&h=600&fit=crop',
    recipe_url: 'https://www.recipetineats.com/beef-stir-fry/',
    user_id: '',
  },
  {
    title: 'French Onion Soup',
    description:
      'Rich and comforting soup with caramelized onions, beef broth, and melted Gruyère cheese. A French bistro classic.',
    image_url:
      'https://images.unsplash.com/photo-**********-23ac45744acd?w=800&h=600&fit=crop',
    recipe_url:
      'https://www.foodnetwork.com/recipes/tyler-florence/french-onion-soup-recipe-1947434',
    user_id: '',
  },
  {
    title: 'Salmon Teriyaki',
    description:
      'Glazed salmon fillets with a sweet and savory teriyaki sauce. Healthy, flavorful, and ready in under 30 minutes.',
    image_url:
      'https://images.unsplash.com/photo-1467003909585-2f8a72700288?w=800&h=600&fit=crop',
    recipe_url:
      'https://www.allrecipes.com/recipe/212721/simple-teriyaki-salmon/',
    user_id: '',
  },
  {
    title: 'Mushroom Risotto',
    description:
      'Creamy Italian rice dish with sautéed mushrooms, white wine, and Parmesan cheese. Comfort food at its finest.',
    image_url:
      'https://images.unsplash.com/photo-1476124369491-e7addf5db371?w=800&h=600&fit=crop',
    recipe_url: 'https://www.bonappetit.com/recipe/mushroom-risotto',
    user_id: '',
  },
  {
    title: 'BBQ Pulled Pork',
    description:
      'Slow-cooked pork shoulder with tangy BBQ sauce, perfect for sandwiches. Tender, smoky, and full of flavor.',
    image_url:
      'https://images.unsplash.com/photo-**********-d76694265947?w=800&h=600&fit=crop',
    recipe_url: 'https://www.foodnetwork.com/recipes/pulled-pork-recipe',
    user_id: '',
  },
  {
    title: 'Greek Salad',
    description:
      'Fresh Mediterranean salad with tomatoes, cucumbers, olives, feta cheese, and olive oil dressing. Light and refreshing.',
    image_url:
      'https://images.unsplash.com/photo-1540420773420-3366772f4999?w=800&h=600&fit=crop',
    recipe_url: 'https://www.mediterraneanliving.com/recipe/greek-salad/',
    user_id: '',
  },
  {
    title: 'Pancakes',
    description:
      'Fluffy American-style pancakes perfect for weekend breakfast. Serve with maple syrup and butter for the ultimate treat.',
    image_url:
      'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=800&h=600&fit=crop',
    recipe_url:
      'https://www.allrecipes.com/recipe/21014/good-old-fashioned-pancakes/',
    user_id: '',
  },
  {
    title: 'Ramen Bowl',
    description:
      'Homemade ramen with rich broth, soft-boiled eggs, and fresh toppings. Comfort in a bowl with authentic Japanese flavors.',
    image_url:
      'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?w=800&h=600&fit=crop',
    recipe_url: 'https://www.seriouseats.com/tonkotsu-ramen-recipe',
    user_id: '',
  },
];

async function seedRecipes() {
  console.log('🌱 Starting to seed recipes...');

  try {
    for (let i = 0; i < exampleRecipes.length; i++) {
      const recipe = exampleRecipes[i];
      console.log(
        `📝 Adding recipe ${i + 1}/${exampleRecipes.length}: ${recipe.title}`
      );

      await createRecipe(recipe);

      // Add a small delay to avoid overwhelming the database
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    console.log('✅ Successfully added all example recipes!');
    console.log(`📊 Total recipes added: ${exampleRecipes.length}`);
  } catch (error) {
    console.error('❌ Error seeding recipes:', error);
    process.exit(1);
  }
}

// Run the seed function if this script is executed directly
if (require.main === module) {
  seedRecipes();
}

export { seedRecipes, exampleRecipes };
