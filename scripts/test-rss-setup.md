# RSS Feed Setup Testing Guide

This guide helps you test the RSS feed integration setup step by step.

## Step 1: Check Database Setup

First, verify that the database tables are created correctly:

### Option A: Use the Test Endpoint
1. Start your development server: `npm run dev`
2. Log into your app at `http://localhost:3000`
3. Visit: `http://localhost:3000/api/rss-feeds/test`

You should see a response like:
```json
{
  "user": {
    "id": "your-user-id",
    "email": "<EMAIL>"
  },
  "tables": {
    "rss_feeds": {
      "exists": true,
      "error": null,
      "code": null
    },
    "rss_import_logs": {
      "exists": true,
      "error": null,
      "code": null
    },
    "recipes_rss_columns": {
      "exists": true,
      "error": null,
      "code": null
    }
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

### Option B: Manual Database Check
1. Go to your Supabase Dashboard
2. Navigate to **Table Editor**
3. Verify these tables exist:
   - `rss_feeds`
   - `rss_import_logs`
   - `recipes` (should have `rss_feed_id` and `rss_item_guid` columns)

## Step 2: Get Setup Instructions (If Tables Don't Exist)

If any tables are missing:

1. Visit: `http://localhost:3000/api/rss-feeds/setup`
2. Copy the SQL commands from the response
3. Go to **Supabase Dashboard > SQL Editor**
4. Paste and run the SQL commands
5. Re-test using Step 1

## Step 3: Test RSS Feed Creation

1. Navigate to: `http://localhost:3000/rss-feeds`
2. Click **"Add Feed"**
3. Test with this RSS feed: `https://www.epicurious.com/feed/recipes/rss`
4. Click **"Test"** - you should see sample recipes
5. Enter a name like "Epicurious Recipes"
6. Click **"Create Feed"**

If successful, you should see the feed in your list!

## Step 4: Test Recipe Import

1. Find your newly created feed in the list
2. Click **"Import"** on the feed
3. Wait for the import to complete
4. Check your main recipes page to see imported recipes

## Step 5: Test Scheduled Import

1. In the RSS Feeds page, find the **"Scheduled Imports"** section
2. Click **"Import Now"** to test bulk import
3. Monitor the progress in the modal

## Common Issues and Solutions

### Issue: "Failed to create RSS feed: new row violates row-level security policy"
**Solution**: The database tables weren't created with the correct RLS policies.
1. Run the SQL setup from `/api/rss-feeds/setup`
2. Make sure all RLS policies are created correctly

### Issue: "RSS feed URL already exists"
**Solution**: You're trying to add a feed that already exists.
1. Check your existing feeds
2. Use a different RSS URL or delete the existing one

### Issue: "RSS feed test failed"
**Solution**: The RSS URL might be invalid or inaccessible.
1. Verify the URL works in your browser
2. Try a different RSS feed URL
3. Check if the website blocks automated requests

### Issue: No recipes imported
**Solution**: The RSS feed might not contain recipe content.
1. Check the feed content manually
2. Try a different recipe-focused RSS feed
3. Look at the import logs for error details

## Popular RSS Feeds for Testing

Here are some reliable RSS feeds you can use for testing:

1. **Epicurious**: `https://www.epicurious.com/feed/recipes/rss`
2. **The Kitchn**: `https://www.thekitchn.com/main.rss`
3. **Food52**: `https://food52.com/blog.rss`
4. **Serious Eats**: `https://www.seriouseats.com/rss.xml`

## Debugging Tips

1. **Check Browser Console**: Look for JavaScript errors
2. **Check Network Tab**: See if API calls are failing
3. **Check Server Logs**: Look at your terminal for server errors
4. **Use Test Endpoints**: Use `/api/rss-feeds/test` to verify setup
5. **Check Import Logs**: Visit the RSS Feeds page to see import history

## Success Indicators

✅ **Database Setup Complete**: All tables show `"exists": true` in test endpoint
✅ **Feed Creation Working**: Can add RSS feeds without errors
✅ **Feed Testing Working**: Can validate RSS feeds and see sample items
✅ **Import Working**: Can import recipes from RSS feeds
✅ **Recipes Visible**: Imported recipes appear in main recipes list

If all these work, your RSS feed integration is fully functional! 🎉
