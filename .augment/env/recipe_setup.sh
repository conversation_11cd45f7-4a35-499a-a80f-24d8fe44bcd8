#!/bin/bash
set -e

echo "Setting up development environment for Xcode project analysis..."

# Update system packages
sudo apt-get update

echo "Installing basic development tools..."

# Install basic build tools and utilities (excluding awk which is virtual)
sudo apt-get install -y \
    build-essential \
    git \
    curl \
    wget \
    tree \
    file \
    grep \
    gawk \
    sed

# Navigate to project directory
cd /mnt/persist/workspace

# Verify project structure
echo "Analyzing project structure..."

# Check if this is a valid Xcode project
if [ ! -f "Prototypes.xcodeproj/project.pbxproj" ]; then
    echo "Error: Xcode project file not found"
    exit 1
fi

echo "✓ Found Xcode project: Prototypes.xcodeproj"

# Check for Swift source files
SWIFT_FILES=$(find . -name "*.swift" -type f | wc -l)
echo "✓ Found $SWIFT_FILES Swift source files"

# Check for test files
TEST_FILES=$(find . -name "*Test*.swift" -o -name "*test*.swift" | wc -l)
echo "✓ Found $TEST_FILES test-related Swift files"

# List the main project structure
echo "Project structure:"
tree -L 3 -I ".git|*.xcuserstate|xcuserdata" . || ls -la

# Analyze the Xcode project file for targets
echo "Analyzing Xcode project targets..."
if grep -q "PBXNativeTarget" Prototypes.xcodeproj/project.pbxproj; then
    echo "✓ Found native targets in Xcode project"
    
    # Extract target names
    TARGET_COUNT=$(grep -c "isa = PBXNativeTarget" Prototypes.xcodeproj/project.pbxproj)
    echo "✓ Found $TARGET_COUNT target(s)"
    
    # Check for test targets specifically
    if grep -q "\.xctest" Prototypes.xcodeproj/project.pbxproj; then
        echo "✓ Found test targets in project"
    else
        echo "⚠ No formal test targets found in project"
    fi
else
    echo "⚠ No native targets found"
fi

# Check for Swift Package Manager dependencies
if [ -f "Prototypes.xcodeproj/project.xcworkspace/xcshareddata/swiftpm/Package.resolved" ]; then
    echo "✓ Found Swift Package Manager dependencies"
    PACKAGE_COUNT=$(grep -c '"identity"' Prototypes.xcodeproj/project.xcworkspace/xcshareddata/swiftpm/Package.resolved)
    echo "✓ Found $PACKAGE_COUNT Swift packages"
else
    echo "⚠ No Swift Package Manager dependencies found"
fi

# Analyze test files content
echo "Analyzing test files..."
for test_file in $(find . -name "*Test*.swift" -o -name "*test*.swift"); do
    echo "Analyzing: $test_file"
    
    # Check if it's a proper XCTest file
    if grep -q "import XCTest" "$test_file"; then
        echo "  ✓ Contains XCTest imports"
    else
        echo "  ⚠ No XCTest imports found"
    fi
    
    # Check for test methods
    TEST_METHODS=$(grep -c "func test" "$test_file" || echo "0")
    echo "  ✓ Found $TEST_METHODS test methods"
done

# Create a simple validation script
echo "Creating project validation script..."
cat > validate_project.sh << 'EOF'
#!/bin/bash

echo "=== Project Validation Report ==="
echo "Date: $(date)"
echo "Project: Prototypes (Swift/Xcode macOS Application)"
echo

# Count source files
SWIFT_COUNT=$(find . -name "*.swift" -not -path "./Tests/*" -not -path "./.build/*" | wc -l)
echo "Swift source files: $SWIFT_COUNT"

# Count test files
TEST_COUNT=$(find . -name "*Test*.swift" -o -name "*test*.swift" | wc -l)
echo "Test files: $TEST_COUNT"

# Check project file integrity
if [ -f "Prototypes.xcodeproj/project.pbxproj" ]; then
    echo "✓ Xcode project file exists and is readable"
    
    # Basic syntax check (skip plutil as it's macOS specific)
    if [ -r "Prototypes.xcodeproj/project.pbxproj" ]; then
        echo "✓ Xcode project file is readable"
    else
        echo "⚠ Xcode project file may have permission issues"
    fi
else
    echo "✗ Xcode project file missing"
    exit 1
fi

# Check for common Swift syntax issues in source files
echo "Checking Swift files for basic syntax..."
SYNTAX_ERRORS=0
for swift_file in $(find . -name "*.swift" -not -path "./.build/*"); do
    # Basic checks for common syntax issues
    if grep -q "import.*import" "$swift_file"; then
        echo "⚠ Potential duplicate imports in $swift_file"
        SYNTAX_ERRORS=$((SYNTAX_ERRORS + 1))
    fi
    
    # Check for unmatched braces (basic check)
    OPEN_BRACES=$(grep -o '{' "$swift_file" | wc -l)
    CLOSE_BRACES=$(grep -o '}' "$swift_file" | wc -l)
    if [ "$OPEN_BRACES" -ne "$CLOSE_BRACES" ]; then
        echo "⚠ Unmatched braces in $swift_file (${OPEN_BRACES} open, ${CLOSE_BRACES} close)"
        SYNTAX_ERRORS=$((SYNTAX_ERRORS + 1))
    fi
done

if [ "$SYNTAX_ERRORS" -eq 0 ]; then
    echo "✓ No obvious syntax errors found"
else
    echo "⚠ Found $SYNTAX_ERRORS potential syntax issues"
fi

echo
echo "=== Summary ==="
echo "This is a macOS Swift application built with Xcode."
echo "The project contains $SWIFT_COUNT Swift source files and $TEST_COUNT test files."
echo "Since this is an Xcode project without formal XCTest targets,"
echo "traditional unit testing requires Xcode or xcodebuild on macOS."
echo
echo "Project validation completed successfully."
EOF

chmod +x validate_project.sh

echo "Development environment setup complete"
echo "Created validation script: validate_project.sh"