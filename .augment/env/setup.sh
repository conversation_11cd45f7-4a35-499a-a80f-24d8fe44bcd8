#!/bin/bash
set -e

# Update system packages
sudo apt-get update

# Install Node.js 20 (LTS) using NodeSource repository
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify Node.js and npm installation
node --version
npm --version

# Install pnpm globally with sudo
sudo npm install -g pnpm

# Verify pnpm installation
pnpm --version

# Navigate to the project directory
cd /mnt/persist/workspace

# Install dependencies using pnpm
pnpm install

# Fix the failing test by updating the button text from "delete" to "archive"
sed -i 's/const deleteButton = screen.getByRole('\''button'\'', { name: \/delete\/i });/const deleteButton = screen.getByRole('\''button'\'', { name: \/archive\/i });/' src/components/__tests__/RecipeCard.test.tsx
sed -i 's/\/\/ Check that the delete button is present/\/\/ Check that the archive button is present/' src/components/__tests__/RecipeCard.test.tsx

# Add Node.js and npm to PATH in user profile
echo 'export PATH="/usr/bin:$PATH"' >> $HOME/.profile

# Source the profile to make sure PATH is updated
source $HOME/.profile