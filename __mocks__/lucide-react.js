// Mock for lucide-react icons
const React = require('react');

const createMockIcon = (name) => {
  return React.forwardRef((props, ref) => {
    return React.createElement('svg', {
      ...props,
      ref,
      'data-testid': `${name}-icon`,
      'aria-label': name,
    });
  });
};

module.exports = {
  X: createMockIcon('X'),
  Plus: createMockIcon('Plus'),
  Edit: createMockIcon('Edit'),
  Trash2: createMockIcon('Trash2'),
  ExternalLink: createMockIcon('ExternalLink'),
  Moon: createMockIcon('Moon'),
  Sun: createMockIcon('Sun'),
  Monitor: createMockIcon('Monitor'),
  // Add more icons as needed
};
